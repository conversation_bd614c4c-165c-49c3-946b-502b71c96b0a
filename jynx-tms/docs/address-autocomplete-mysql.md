# 基于MySQL的地址补全功能

## 功能概述

由于线上环境未配置Elasticsearch且担心资源消耗过大，在TmsBaseCityStreetPostcodeController中实现了基于MySQL的地址补全功能。

## 主要功能

### 1. 地址补全接口

**接口地址：** `GET /tmsBaseCityStreetPostcode/autocomplete`

**请求参数：**
- `postcode`（String，必填）：六位邮编
- `keyword`（String，可选）：搜索关键词
- `limit`（Integer，必填）：返回条数限制，最大值30

**响应格式：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "threePostCode": "M5V",
      "sixPostCode": "M5V3A8",
      "city": "Toronto",
      "province": "ON",
      "street": "123 Main St",
      "fullAddress": "123 Main St, Toronto, ON M5V3A8",
      "matchType": "Address"
    }
  ]
}
```

### 2. 业务逻辑

#### 场景A - 邮编存在于数据库
- 实现智能匹配算法，根据匹配度排序返回最相关的记录
- 当`keyword`为空时：仅使用六位邮编进行地址推荐
- 当`keyword`不为空时：结合六位邮编和关键词进行地址推荐，关键词匹配省份、城市、街道三个维度

#### 场景B - 邮编不存在于数据库
- 自动调用加拿大邮政API获取数据
- API调用参数：
  - `Text`：搜索内容（邮编+关键词或仅邮编）
  - `Limit`：返回条数
  - 其他参数保持固定值
- 统一返回数据格式

### 3. 定时任务

**任务名称：** `syncRecentOrderPostcodes`

**执行时间：** 建议每天凌晨2点执行（Cron表达式：`0 0 2 * * ?`）

**功能：**
- 扫描TmsCustomerOrderEntity表最近24小时内出现的六位邮编
- 检查这些邮编是否已存在于`tms_base_city_street_postcode`表中
- 对于不存在的邮编，将客户订单中的地址信息按照`TmsBaseCityStreetPostcodeEntity`实体类字段结构存入数据库
- 目标：逐步完善本地地址库数据

## 技术实现

### 核心类

1. **TmsBaseCityStreetPostcodeController**
   - 提供地址补全REST API接口
   - 参数校验和响应格式化

2. **TmsBaseCityStreetPostcodeService**
   - 业务逻辑接口定义
   - 包含地址搜索、API调用、定时同步方法

3. **TmsBaseCityStreetPostcodeServiceImpl**
   - 核心业务逻辑实现
   - MySQL查询优化
   - 加拿大邮政API集成
   - 地址解析算法

4. **AddressSyncTask**
   - XXL-Job定时任务实现
   - 订单邮编扫描和同步

### 数据库查询策略

```sql
-- 基础查询（仅邮编）
SELECT * FROM tms_base_city_street_postcode 
WHERE status = 1 
  AND six_post_code = 'M5V3A8'
ORDER BY province, city, rstreet
LIMIT 10;

-- 关键词查询（邮编+关键词）
SELECT * FROM tms_base_city_street_postcode 
WHERE status = 1 
  AND six_post_code = 'M5V3A8'
  AND (province LIKE '%TORONTO%' 
    OR city LIKE '%TORONTO%' 
    OR rstreet LIKE '%TORONTO%')
ORDER BY province, city, rstreet
LIMIT 10;
```

### 加拿大邮政API集成

**API端点：** `https://ws1.postescanada-canadapost.ca/Capture/Interactive/Find/v1.00/json3ex.ws`

**关键参数：**
- `Key`: EA98-JC42-TF94-JK98
- `Text`: 搜索文本（邮编+关键词）
- `Limit`: 返回条数
- `Origin`: CAN
- `Countries`: CAN
- `Language`: en

## 使用示例

### 1. 基础邮编查询
```bash
curl -X GET "http://localhost:8080/tmsBaseCityStreetPostcode/autocomplete?postcode=M5V3A8&limit=10"
```

### 2. 邮编+关键词查询
```bash
curl -X GET "http://localhost:8080/tmsBaseCityStreetPostcode/autocomplete?postcode=M5V3A8&keyword=Toronto&limit=10"
```

## 配置说明

### XXL-Job任务配置

1. 登录XXL-Job管理后台
2. 新建任务：
   - 任务名称：地址库同步任务
   - JobHandler：`syncRecentOrderPostcodes`
   - Cron表达式：`0 0 2 * * ?`
   - 任务描述：扫描最近24小时订单邮编并同步到地址库

## 性能优化

1. **数据库索引**
   - `six_post_code` 字段建立索引
   - `status` 字段建立索引
   - 复合索引：`(status, six_post_code)`

2. **查询优化**
   - 使用LIMIT限制返回结果
   - 按相关性排序减少无效数据
   - 缓存常用邮编查询结果

3. **API调用优化**
   - 仅在数据库无数据时调用API
   - 设置合理的超时时间
   - 错误重试机制

## 注意事项

1. 邮编必须为6位加拿大邮编格式
2. 返回条数限制最大为30条
3. API调用需要网络连接，建议设置超时处理
4. 定时任务执行时间建议避开业务高峰期
5. 地址解析算法可根据实际API响应格式调整

## 错误处理

- 参数校验失败：返回具体错误信息
- 数据库查询异常：记录日志并返回空结果
- API调用失败：记录日志并返回空结果
- 定时任务异常：记录详细错误日志
