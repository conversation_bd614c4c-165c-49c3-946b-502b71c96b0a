package com.jygjexp.jynx.tms.controller;

import cn.hutool.json.JSONObject;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.service.CitiesService;
import com.jygjexp.jynx.tms.service.CountriesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * description : 获取加拿大、中国的省/市/区
 * @param:
 * @return {@link null}
 * @Date   2025/3/3
*/
@RestController
@RequiredArgsConstructor
@RequestMapping("/caRegion")
@Tag(description = "caRegion", name = "卡派省/市/区管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CaRegionController {

    private final CountriesService countriesService;

    /**
     * 获取加拿大的省/市/区
     *
     * @return
     */
    @Operation(summary = "获取加拿大的省/市/区", description = "获取加拿大的省/市/区")
    @PostMapping("/getCaRegion")
    public JSONObject getCaRegion() {
        return countriesService.getCaRegion();
    }

    /**
     * 获取中国的省/市/区
     *
     * @return
     */
    @Operation(summary = "获取中国的省/市/区", description = "获取中国的省/市/区")
    @PostMapping("/getCNRegion")
    @Inner(value = false)
    public JSONObject getCNRegion() {
        return countriesService.getCNRegion();
    }

}