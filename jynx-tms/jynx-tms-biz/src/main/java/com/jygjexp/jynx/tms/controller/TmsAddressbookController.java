package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsAddressbookEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsAddressbookService;
import com.jygjexp.jynx.tms.vo.excel.TmsAddressBookExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 卡派地址簿
 *
 * <AUTHOR>
 * @date 2025-03-03 20:42:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsAddressbook" )
@Tag(description = "tmsAddressbook" , name = "卡派地址簿管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsAddressbookController {

    private final  TmsAddressbookService tmsAddressbookService;

    /**
     * 地址簿分页查询
     * @param page 分页对象
     * @param tmsAddressbook 卡派地址簿
     * @return
     */
    @Operation(summary = "地址簿分页查询" , description = "地址簿分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_view')" )
    public R getTmsAddressbookPage(@ParameterObject Page page, @ParameterObject TmsAddressbookEntity tmsAddressbook) {
        LambdaQueryWrapper<TmsAddressbookEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(tmsAddressbook.getContacts()), TmsAddressbookEntity::getContacts, tmsAddressbook.getContacts())
                .like(StrUtil.isNotBlank(tmsAddressbook.getContactPhone()), TmsAddressbookEntity::getContactPhone, tmsAddressbook.getContactPhone())
                .like(StrUtil.isNotBlank(tmsAddressbook.getPostalCode()), TmsAddressbookEntity::getPostalCode, tmsAddressbook.getPostalCode())
                .like(StrUtil.isNotBlank(tmsAddressbook.getCountryName()), TmsAddressbookEntity::getCountryName, tmsAddressbook.getCountryName())
                .like(StrUtil.isNotBlank(tmsAddressbook.getStatesName()), TmsAddressbookEntity::getStatesName, tmsAddressbook.getStatesName())
                .like(StrUtil.isNotBlank(tmsAddressbook.getCityName()), TmsAddressbookEntity::getCityName, tmsAddressbook.getCityName())
                .eq(ObjectUtil.isNotNull(tmsAddressbook.getAddressType()), TmsAddressbookEntity::getAddressType, tmsAddressbook.getAddressType())
                .orderByDesc(TmsAddressbookEntity::getId);
        return R.ok(tmsAddressbookService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派地址簿
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsAddressbookService.getById(id));
    }

    /**
     * 新增卡派地址簿
     * @param tmsAddressbook 卡派地址簿
     * @return R
     */
    @Operation(summary = "新增卡派地址簿" , description = "新增卡派地址簿" )
    @SysLog("新增卡派地址簿" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_add')" )
    public R save(@RequestBody TmsAddressbookEntity tmsAddressbook) {
        // 校验地址重复性
        if (tmsAddressbookService.checkDuplicate(tmsAddressbook)) {
            return LocalizedR.failed("tms.duplicate.address", "");
        }
        return R.ok(tmsAddressbookService.save(tmsAddressbook));
    }

    /**
     * 修改卡派地址簿
     * @param tmsAddressbook 卡派地址簿
     * @return R
     */
    @Operation(summary = "修改卡派地址簿" , description = "修改卡派地址簿" )
    @SysLog("修改卡派地址簿" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_edit')" )
    public R updateById(@RequestBody TmsAddressbookEntity tmsAddressbook) {
        // 校验地址重复性
        if (tmsAddressbookService.checkDuplicate(tmsAddressbook)) {
            return LocalizedR.failed("tms.duplicate.address", "");
        }
        return R.ok(tmsAddressbookService.updateById(tmsAddressbook));
    }

    /**
     * 通过id删除卡派地址簿
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派地址簿" , description = "通过id删除卡派地址簿" )
    @SysLog("通过id删除卡派地址簿" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsAddressbookService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsAddressbook 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressbook_export')" )
    public List<TmsAddressBookExcelVo> export(TmsAddressbookEntity tmsAddressbook, Long[] ids) {
        return tmsAddressbookService.getAddressExcel(tmsAddressbook, ids);
    }
}