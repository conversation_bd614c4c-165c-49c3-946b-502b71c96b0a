package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBasicFreightEntity;
import com.jygjexp.jynx.tms.service.TmsBasicFreightService;
import com.jygjexp.jynx.tms.vo.TmsBasicFreightPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 基础运费表
 *
 * <AUTHOR>
 * @date 2025-03-06 10:48:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBasicFreight" )
@Tag(description = "tmsBasicFreight" , name = "基础运费表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBasicFreightController {

    private final  TmsBasicFreightService tmsBasicFreightService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 基础运费表分页查询参数
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_view')" )
    public R getTmsBasicFreightPage(@ParameterObject Page page, @ParameterObject TmsBasicFreightPageVo vo) {
        return R.ok(tmsBasicFreightService.search(page, vo));
    }


    /**
     * 通过id查询基础运费表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsBasicFreightService.getById(id));
    }

    /**
     * 新增基础运费表
     * @param tmsBasicFreight 基础运费表
     * @return R
     */
    @Operation(summary = "新增基础运费表" , description = "新增基础运费表" )
    @SysLog("新增基础运费表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_add')" )
    public R save(@RequestBody TmsBasicFreightEntity tmsBasicFreight) {
        return R.ok(tmsBasicFreightService.save(tmsBasicFreight));
    }

    /**
     * 修改基础运费表
     * @param tmsBasicFreight 基础运费表
     * @return R
     */
    @Operation(summary = "修改基础运费表" , description = "修改基础运费表" )
    @SysLog("修改基础运费表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_edit')" )
    public R updateById(@RequestBody TmsBasicFreightEntity tmsBasicFreight) {
        return R.ok(tmsBasicFreightService.updateById(tmsBasicFreight));
    }

    /**
     * 通过id删除基础运费表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除基础运费表" , description = "通过id删除基础运费表" )
    @SysLog("通过id删除基础运费表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsBasicFreightService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsBasicFreight 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreight_export')" )
    public List<TmsBasicFreightEntity> export(TmsBasicFreightEntity tmsBasicFreight,Long[] ids) {
        return tmsBasicFreightService.list(Wrappers.lambdaQuery(tmsBasicFreight).in(ArrayUtil.isNotEmpty(ids), TmsBasicFreightEntity::getId, ids));
    }
}