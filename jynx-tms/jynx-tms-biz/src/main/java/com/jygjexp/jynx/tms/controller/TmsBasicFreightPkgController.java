package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBasicFreightPkgEntity;
import com.jygjexp.jynx.tms.service.TmsBasicFreightPkgService;
import com.jygjexp.jynx.tms.vo.TmsBasicFreightPkgPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 包裹基础运费表-模版
 *
 * <AUTHOR>
 * @date 2025-03-07 14:20:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBasicFreightPkg" )
@Tag(description = "tmsBasicFreightPkg" , name = "包裹基础运费表-模版管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBasicFreightPkgController {

    private final  TmsBasicFreightPkgService tmsBasicFreightPkgService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 包裹基础运费表-模版
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_view')" )
    public R getTmsBasicFreightPkgPage(@ParameterObject Page page, @ParameterObject TmsBasicFreightPkgPageVo vo) {
        return R.ok(tmsBasicFreightPkgService.search(page, vo));
    }


    /**
     * 通过id查询包裹基础运费表-模版
     * @param pkgFreId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pkgFreId}")
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_view')" )
    public R getById(@PathVariable("pkgFreId" ) Long pkgFreId) {
        return R.ok(tmsBasicFreightPkgService.getById(pkgFreId));
    }

    /**
     * 新增包裹基础运费表-模版
     * @param tmsBasicFreightPkg 包裹基础运费表-模版
     * @return R
     */
    @Operation(summary = "新增包裹基础运费表-模版" , description = "新增包裹基础运费表-模版" )
    @SysLog("新增包裹基础运费表-模版" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_add')" )
    public R save(@RequestBody TmsBasicFreightPkgEntity tmsBasicFreightPkg) {
        return R.ok(tmsBasicFreightPkgService.save(tmsBasicFreightPkg));
    }

    /**
     * 修改包裹基础运费表-模版
     * @param tmsBasicFreightPkg 包裹基础运费表-模版
     * @return R
     */
    @Operation(summary = "修改包裹基础运费表-模版" , description = "修改包裹基础运费表-模版" )
    @SysLog("修改包裹基础运费表-模版" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_edit')" )
    public R updateById(@RequestBody TmsBasicFreightPkgEntity tmsBasicFreightPkg) {
        return R.ok(tmsBasicFreightPkgService.updateById(tmsBasicFreightPkg));
    }

    /**
     * 通过id删除包裹基础运费表-模版
     * @param ids pkgFreId列表
     * @return R
     */
    @Operation(summary = "通过id删除包裹基础运费表-模版" , description = "通过id删除包裹基础运费表-模版" )
    @SysLog("通过id删除包裹基础运费表-模版" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsBasicFreightPkgService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsBasicFreightPkg 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBasicFreightPkg_export')" )
    public List<TmsBasicFreightPkgEntity> export(TmsBasicFreightPkgEntity tmsBasicFreightPkg,Long[] ids) {
        return tmsBasicFreightPkgService.list(Wrappers.lambdaQuery(tmsBasicFreightPkg).in(ArrayUtil.isNotEmpty(ids), TmsBasicFreightPkgEntity::getPkgFreId, ids));
    }
}