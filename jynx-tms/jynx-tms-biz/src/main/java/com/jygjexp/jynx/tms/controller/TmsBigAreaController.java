package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsBigAreaEntity;
import com.jygjexp.jynx.tms.service.TmsBigAreaService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 大区管理
 *
 * <AUTHOR>
 * @date 2025-05-09 14:06:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBigArea" )
@Tag(description = "tmsBigArea" , name = "大区管理管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBigAreaController {

    private final  TmsBigAreaService tmsBigAreaService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsBigArea 大区管理
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @Inner(value = false)
    public R getTmsBigAreaPage(@ParameterObject Page page, @ParameterObject TmsBigAreaEntity tmsBigArea) {
        return tmsBigAreaService.getPage(page , tmsBigArea);
    }


    /**
     * 通过id查询大区管理
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsBigAreaService.getById(id));
    }

    /**
     * 新增大区管理
     * @param tmsBigArea 大区管理
     * @return R
     */
    @Operation(summary = "新增大区管理" , description = "新增大区管理" )
    @SysLog("新增大区管理" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_add')" )
    public R save(@RequestBody TmsBigAreaEntity tmsBigArea) {
     return  tmsBigAreaService.saveBigArea(tmsBigArea);
    }

    /**
     * 修改大区管理
     * @param tmsBigArea 大区管理
     * @return R
     */
    @Operation(summary = "修改大区管理" , description = "修改大区管理" )
    @SysLog("修改大区管理" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_edit')" )
    public R updateById(@RequestBody TmsBigAreaEntity tmsBigArea) {
        return R.ok(tmsBigAreaService.updateById(tmsBigArea));
    }

    /**
     * 通过id删除大区管理
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除大区管理" , description = "通过id删除大区管理" )
    @SysLog("通过id删除大区管理" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsBigAreaService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsBigArea 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_export')" )
    public List<TmsBigAreaEntity> export(TmsBigAreaEntity tmsBigArea,Long[] ids) {
        return tmsBigAreaService.list(Wrappers.lambdaQuery(tmsBigArea).in(ArrayUtil.isNotEmpty(ids), TmsBigAreaEntity::getId, ids));
    }

    /**
     * 查询所有大区
     * @return
     */
    @Operation(summary = "查询所有大区" , description = "查询所有大区" )
    @GetMapping("/getAllBigArea" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsBigArea_list')")
    public R getAllBigArea() {
        return R.ok(tmsBigAreaService.list());
    }
}