package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxEntity;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxRuleEntity;
import com.jygjexp.jynx.tms.service.TmsBlindBoxService;
import com.jygjexp.jynx.tms.vo.*;

import javax.validation.Valid;

import com.jygjexp.jynx.tms.vo.excel.TmsBlindBoxExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 物流商盲盒信息
 *
 * <AUTHOR>
 * @date 2025-07-18 18:00:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBlindBox" )
@Tag(description = "tmsBlindBox" , name = "物流商盲盒信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBlindBoxController {

    private final  TmsBlindBoxService tmsBlindBoxService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsBlindBox 物流商盲盒信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_view')" )
    public R getTmsBlindBoxPage(@ParameterObject Page page, @ParameterObject TmsBlindBoxPageVo tmsBlindBox) {
        return R.ok(tmsBlindBoxService.search(page, tmsBlindBox));
    }


    /**
     * 通过id查询盲盒详情（包含规则配置）
     * @param id id
     * @return R
     */
    @Operation(summary = "查询盲盒详情" , description = "查询盲盒详情（包含规则配置）" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        try {
            TmsBlindBoxDetailVo detail = tmsBlindBoxService.getBlindBoxDetail(id);
            return R.ok(detail);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
    }

    /**
     * 创建盲盒及其规则配置
     * @param createVo 创建请求参数
     * @return R
     */
    @Operation(summary = "创建盲盒" , description = "创建盲盒及其规则配置" )
    @SysLog("创建盲盒" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_add')" )
    public R save(@Valid @RequestBody TmsBlindBoxCreateVo createVo) {
        return tmsBlindBoxService.createBlindBox(createVo);
    }

    /**
     * 更新盲盒及其规则配置
     * @param tmsBlindBox 更新请求参数
     * @return R
     */
    @Operation(summary = "更新盲盒" , description = "更新盲盒及其规则配置" )
    @SysLog("更新盲盒" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_edit')" )
    public R updateById(@RequestBody TmsBlindBoxEntity tmsBlindBox) {
        return tmsBlindBoxService.updateBlindBox(tmsBlindBox);
    }

    /**
     * 级联删除盲盒及其规则配置
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "级联删除盲盒" , description = "删除盲盒及其关联的规则配置" )
    @SysLog("级联删除盲盒" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return tmsBlindBoxService.cascadeDeleteBlindBox(ids);
    }


    /**
     * 导出盲盒数据
     * @param tmsBlindBox 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBox_export')" )
    public List<TmsBlindBoxExcelVo> export(TmsBlindBoxPageVo tmsBlindBox, Long[] ids) {
        return tmsBlindBoxService.exportBlindBox(tmsBlindBox, ids);
    }
}