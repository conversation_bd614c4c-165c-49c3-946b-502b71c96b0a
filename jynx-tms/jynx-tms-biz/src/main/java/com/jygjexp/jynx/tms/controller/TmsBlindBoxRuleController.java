package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxRuleEntity;
import com.jygjexp.jynx.tms.service.TmsBlindBoxRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 盲盒服务商规则配置表
 *
 * <AUTHOR>
 * @date 2025-07-18 18:00:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsBlindBoxRule" )
@Tag(description = "tmsBlindBoxRule" , name = "盲盒服务商规则配置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsBlindBoxRuleController {

    private final  TmsBlindBoxRuleService tmsBlindBoxRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsBlindBoxRule 盲盒服务商规则配置表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_view')" )
    public R getTmsBlindBoxRulePage(@ParameterObject Page page, @ParameterObject TmsBlindBoxRuleEntity tmsBlindBoxRule) {
        LambdaQueryWrapper<TmsBlindBoxRuleEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsBlindBoxRuleService.page(page, wrapper));
    }


    /**
     * 通过id查询盲盒服务商规则配置表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsBlindBoxRuleService.getById(id));
    }

    /**
     * 新增盲盒服务商规则配置表
     * @param tmsBlindBoxRule 盲盒服务商规则配置表
     * @return R
     */
    @Operation(summary = "新增盲盒服务商规则配置表" , description = "新增盲盒服务商规则配置表" )
    @SysLog("新增盲盒服务商规则配置表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_add')" )
    public R save(@RequestBody TmsBlindBoxRuleEntity tmsBlindBoxRule) {
        return R.ok(tmsBlindBoxRuleService.save(tmsBlindBoxRule));
    }

    /**
     * 修改盲盒服务商规则配置表
     * @param tmsBlindBoxRule 盲盒服务商规则配置表
     * @return R
     */
    @Operation(summary = "修改盲盒服务商规则配置表" , description = "修改盲盒服务商规则配置表" )
    @SysLog("修改盲盒服务商规则配置表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_edit')" )
    public R updateById(@RequestBody TmsBlindBoxRuleEntity tmsBlindBoxRule) {
        return R.ok(tmsBlindBoxRuleService.updateById(tmsBlindBoxRule));
    }

    /**
     * 通过id删除盲盒服务商规则配置表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除盲盒服务商规则配置表" , description = "通过id删除盲盒服务商规则配置表" )
    @SysLog("通过id删除盲盒服务商规则配置表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsBlindBoxRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsBlindBoxRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsBlindBoxRule_export')" )
    public List<TmsBlindBoxRuleEntity> export(TmsBlindBoxRuleEntity tmsBlindBoxRule,Long[] ids) {
        return tmsBlindBoxRuleService.list(Wrappers.lambdaQuery(tmsBlindBoxRule).in(ArrayUtil.isNotEmpty(ids), TmsBlindBoxRuleEntity::getId, ids));
    }
}