package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsCageAndOrderEntity;
import com.jygjexp.jynx.tms.service.TmsCageAndOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 笼车与订单绑定表
 *
 * <AUTHOR>
 * @date 2025-04-18 14:10:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCageAndOrder" )
@Tag(description = "tmsCageAndOrder" , name = "笼车与订单绑定表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCageAndOrderController {

    private final  TmsCageAndOrderService tmsCageAndOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsCageAndOrder 笼车与订单绑定表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_view')" )
    public R getTmsCageAndOrderPage(@ParameterObject Page page, @ParameterObject TmsCageAndOrderEntity tmsCageAndOrder) {
        LambdaQueryWrapper<TmsCageAndOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsCageAndOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询笼车与订单绑定表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCageAndOrderService.getById(id));
    }

    /**
     * 新增笼车与订单绑定表
     * @param tmsCageAndOrder 笼车与订单绑定表
     * @return R
     */
    @Operation(summary = "新增笼车与订单绑定表" , description = "新增笼车与订单绑定表" )
    @SysLog("新增笼车与订单绑定表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_add')" )
    public R save(@RequestBody TmsCageAndOrderEntity tmsCageAndOrder) {
        return R.ok(tmsCageAndOrderService.save(tmsCageAndOrder));
    }

    /**
     * 修改笼车与订单绑定表
     * @param tmsCageAndOrder 笼车与订单绑定表
     * @return R
     */
    @Operation(summary = "修改笼车与订单绑定表" , description = "修改笼车与订单绑定表" )
    @SysLog("修改笼车与订单绑定表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_edit')" )
    public R updateById(@RequestBody TmsCageAndOrderEntity tmsCageAndOrder) {
        return R.ok(tmsCageAndOrderService.updateById(tmsCageAndOrder));
    }

    /**
     * 通过id删除笼车与订单绑定表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除笼车与订单绑定表" , description = "通过id删除笼车与订单绑定表" )
    @SysLog("通过id删除笼车与订单绑定表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCageAndOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsCageAndOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCageAndOrder_export')" )
    public List<TmsCageAndOrderEntity> export(TmsCageAndOrderEntity tmsCageAndOrder,Long[] ids) {
        return tmsCageAndOrderService.list(Wrappers.lambdaQuery(tmsCageAndOrder).in(ArrayUtil.isNotEmpty(ids), TmsCageAndOrderEntity::getId, ids));
    }
}