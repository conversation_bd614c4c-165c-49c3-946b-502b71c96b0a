package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsChannel;
import com.jygjexp.jynx.tms.excel.TmsChannelExcelDto;
import com.jygjexp.jynx.tms.service.TmsChannelService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @date 2025-08-22 07:06:55
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsChannel" )
@Tag(description = "tmsChannel" , name = "渠道管理管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsChannelController {

    private final  TmsChannelService tmsChannelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsChannel 渠道管理
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_view')" )
    public R getTmsChannelPage(@ParameterObject Page page, @ParameterObject TmsChannel tmsChannel) {
        return R.ok(tmsChannelService.search(page, tmsChannel));
    }


    /**
     * 通过id查询渠道管理
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsChannelService.getById(id));
    }

    /**
     * 新增渠道管理
     * @param tmsChannel 渠道管理
     * @return R
     */
    @Operation(summary = "新增渠道管理" , description = "新增渠道管理" )
    @SysLog("新增渠道管理" )
    @PostMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_add')" )
    public R save(@RequestBody TmsChannel tmsChannel) {
        return R.ok(tmsChannelService.save(tmsChannel));
    }

    /**
     * 修改渠道管理
     * @param tmsChannel 渠道管理
     * @return R
     */
    @Operation(summary = "修改渠道管理" , description = "修改渠道管理" )
    @SysLog("修改渠道管理" )
    @PutMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_edit')" )
    public R updateById(@RequestBody TmsChannel tmsChannel) {
        return R.ok(tmsChannelService.updateById(tmsChannel));
    }

    /**
     * 通过id删除渠道管理
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除渠道管理" , description = "通过id删除渠道管理" )
    @SysLog("通过id删除渠道管理" )
    @DeleteMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsChannelService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsChannel 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @Operation(summary = "导出")
    @ResponseExcel(i18nHeader = true)
    @GetMapping("/export")
    //@PreAuthorize("@pms.hasPermission('tms_tmsChannel_export')" )
    public List<TmsChannelExcelDto> export(@ParameterObject Page page, @ParameterObject TmsChannel tmsChannel) {
        return tmsChannelService.export(page, tmsChannel);
    }

    /**
     * 启用
     * @param id
     * @return
     */
    @Operation(summary = "启用")
    @PutMapping("/{id}/enable")
    public R<Boolean> enable(@PathVariable Long id){
        return R.ok(tmsChannelService.enable(id));
    }

    /**
     * 禁用
     * @param id
     * @return
     */
    @Operation(summary = "禁用")
    @PutMapping("/{id}/disable")
    public R<Boolean> disable(@PathVariable Long id){
        return R.ok(tmsChannelService.disable(id));
    }

    /**
     * 获取所有渠道
     * @return
     */
    @Operation(summary = "获取所有渠道")
    @GetMapping("/channels")
    public R<List<TmsChannel>> channels(){
        return R.ok(tmsChannelService.channels());
    }
}