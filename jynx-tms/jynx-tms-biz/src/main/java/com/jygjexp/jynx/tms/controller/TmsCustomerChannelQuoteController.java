package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsCustomerChannelQuoteEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCustomerChannelQuoteService;
import com.jygjexp.jynx.tms.vo.TmsCustomerChannelQuotePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 客户渠道报价
 *
 * <AUTHOR>
 * @date 2025-08-22 16:32:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCustomerChannelQuote" )
@Tag(description = "tmsCustomerChannelQuote" , name = "客户渠道报价管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCustomerChannelQuoteController {

    private final  TmsCustomerChannelQuoteService tmsCustomerChannelQuoteService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 客户渠道报价
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_view')" )
    public R getTmsCustomerChannelQuotePage(@ParameterObject Page page, @ParameterObject TmsCustomerChannelQuotePageVo vo) {
        return R.ok(tmsCustomerChannelQuoteService.search(page, vo));
    }


    /**
     * 通过id查询客户渠道报价
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCustomerChannelQuoteService.getById(id));
    }

    /**
     * 新增客户渠道报价
     * @param tmsCustomerChannelQuote 客户渠道报价
     * @return R
     */
    @Operation(summary = "新增客户渠道报价" , description = "新增客户渠道报价" )
    @SysLog("新增客户渠道报价" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_add')" )
    public R save(@RequestBody TmsCustomerChannelQuoteEntity tmsCustomerChannelQuote) {
        if (!checkChannelName(tmsCustomerChannelQuote)) {
            return LocalizedR.failed("tms.customerChannelQuote.name.exists", Optional.ofNullable(null));
        }
        return R.ok(tmsCustomerChannelQuoteService.save(tmsCustomerChannelQuote));
    }

    /**
     * 修改客户渠道报价
     * @param tmsCustomerChannelQuote 客户渠道报价
     * @return R
     */
    @Operation(summary = "修改客户渠道报价" , description = "修改客户渠道报价" )
    @SysLog("修改客户渠道报价" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_edit')" )
    public R updateById(@RequestBody TmsCustomerChannelQuoteEntity tmsCustomerChannelQuote) {
        if (!checkChannelName(tmsCustomerChannelQuote)) {
            return LocalizedR.failed("tms.customerChannelQuote.name.exists", Optional.ofNullable(null));
        }
        return R.ok(tmsCustomerChannelQuoteService.updateById(tmsCustomerChannelQuote));
    }

    /**
     * 通过id删除客户渠道报价
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除客户渠道报价" , description = "通过id删除客户渠道报价" )
    @SysLog("通过id删除客户渠道报价" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCustomerChannelQuoteService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 判断客户-渠道不可重复
    public Boolean checkChannelName(TmsCustomerChannelQuoteEntity tmsCustomerChannelQuote) {
        LambdaQueryWrapper<TmsCustomerChannelQuoteEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TmsCustomerChannelQuoteEntity::getChannelId, tmsCustomerChannelQuote.getChannelId())
                .eq(TmsCustomerChannelQuoteEntity::getCustomerId, tmsCustomerChannelQuote.getCustomerId());
        queryWrapper.ne(ObjectUtil.isNotNull(tmsCustomerChannelQuote.getId()),TmsCustomerChannelQuoteEntity::getId, tmsCustomerChannelQuote.getId());
        TmsCustomerChannelQuoteEntity byName = tmsCustomerChannelQuoteService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }



    /**
     * 导出excel 表格
     * @param tmsCustomerChannelQuote 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerChannelQuote_export')" )
    public List<TmsCustomerChannelQuoteEntity> export(TmsCustomerChannelQuoteEntity tmsCustomerChannelQuote,Long[] ids) {
        return tmsCustomerChannelQuoteService.list(Wrappers.lambdaQuery(tmsCustomerChannelQuote).in(ArrayUtil.isNotEmpty(ids), TmsCustomerChannelQuoteEntity::getId, ids));
    }*/
}