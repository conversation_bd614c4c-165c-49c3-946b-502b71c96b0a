package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysRole;
import com.jygjexp.jynx.admin.api.feign.RemoteRoleService;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.entity.TmsCarrierEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsDriverEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡派-客户信息
 *
 * <AUTHOR>
 * @date 2025-02-24 15:46:47
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCustomer" )
@Tag(description = "tmsCustomer" , name = "卡派-客户信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCustomerController {

    private final  TmsCustomerService tmsCustomerService;
    private final RemoteTmsAppUserService remoteTmsAppUserService;
    private final RemoteTmsUpmsService remoteTmsUpmsService;



    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsCustomer 卡派-客户信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_view')" )
    public R getTmsCustomerPage(@ParameterObject Page page, @ParameterObject TmsCustomerEntity tmsCustomer) {
        return R.ok(tmsCustomerService.search(page, tmsCustomer));
    }

    /**
     * 查询所有客户列表
     * @return R
     */
    @Operation(summary = "查询所有客户列表", description = "查询所有客户列表")
    @GetMapping("/list")
    @Inner(value = false)
    public R list() {
        // 查询所有客户列表
        List<TmsCustomerEntity> customerList = tmsCustomerService.list();
        return R.ok(customerList);
    }

    /**
     * 通过id查询卡派-客户信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCustomerService.getById(id));
    }



    /**
     * 根据客户编码查询客户信息
     * @return R
     */
    @Operation(summary = "根据客户编码查询客户信息" , description = "根据客户编码查询客户信息" )
    @GetMapping("/getByCode" )
    @Inner(value = false)
    public TmsCustomerEntity getCustomerByCode(String code) {
        return tmsCustomerService.getCustomerByCode(code);
    }


    /**
     * 根据客户token查询客户信息
     * @return R
     */
    @Operation(summary = "根据客户token查询客户信息" , description = "根据客户token查询客户信息" )
    @GetMapping("/getByToken" )
    @Inner(value = false)
    public TmsCustomerEntity getCustomerByToken(String token) {
        return tmsCustomerService.getCustomerByToken(token);
    }


    /**
     * 根据客户USER_ID查询客户信息
     * @return R
     */
    @Operation(summary = "根据客户token查询客户信息" , description = "根据客户token查询客户信息" )
    @GetMapping("/getByUserId" )
    @Inner(value = false)
    public TmsCustomerEntity getCustomerByToken(Long userId) {
        return tmsCustomerService.getCustomerByUserId(userId);
    }

    /**
     * 新增卡派-客户信息表
     * @param tmsCustomer 卡派-客户信息表
     * @return R
     */
    @Operation(summary = "新增卡派-客户信息表" , description = "新增卡派-客户信息表" )
    @SysLog("新增卡派-客户信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_add')" )
    public R save(@RequestBody TmsCustomerEntity tmsCustomer) {
        return R.ok(tmsCustomerService.saveCustomer(tmsCustomer));
    }

    /**
     * 修改卡派-客户信息表
     * @param tmsCustomer 卡派-客户信息表
     * @return R
     */
    @Operation(summary = "修改卡派-客户信息表" , description = "修改卡派-客户信息表" )
    @SysLog("修改卡派-客户信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_edit')" )
    public R updateById(@RequestBody TmsCustomerEntity tmsCustomer) {
        return tmsCustomerService.updateCustomer(tmsCustomer);
    }

    // 客户修改密码
    @Operation(summary = "客户修改密码" , description = "客户修改密码" )
    @SysLog("客户修改密码" )
    @PutMapping("/updatePassword")
    public R updatePassword(@RequestBody TmsCustomerEntity tmsCustomer) {
        return R.ok(tmsCustomerService.updatePassword(tmsCustomer));
    }

    /**
     * 通过id删除卡派-客户信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派-客户信息表" , description = "通过id删除卡派-客户信息表" )
    @SysLog("通过id删除卡派-客户信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_del')" )
    public R removeById(@RequestBody Long[] ids) {
        // 根据ids批量查询客户信息
        List<TmsCustomerEntity> customerList = tmsCustomerService.listByIds(CollUtil.toList(ids));
        for (TmsCustomerEntity customer : customerList) {
            // 根据客户名称查询客户端用户账号
            R<UserInfo> info = remoteTmsUpmsService.info(customer.getCustomerName());
            if (info.getCode() == 0) {
                remoteTmsUpmsService.userDel(new Long[]{info.getData().getSysUser().getUserId()});
            }
        }
        return R.ok(tmsCustomerService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 启用客户
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "启用客户", description = "启用客户")
    @GetMapping("/enableById/{id}")
    public R enableById(@PathVariable("id") Long id) {
        return R.ok(tmsCustomerService.enableById(id));
    }

    /**
     * 停用客户
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "停用客户", description = "停用客户")
    @GetMapping("/disableById/{id}")
    public R disableById(@PathVariable("id") Long id) {
        return R.ok(tmsCustomerService.disableById(id));
    }

    /**
     * 根据客户id，查询是否推送UNI(主要用于面单)
     *
     * @param customerId
     * @return R
     */
    @Operation(summary = "根据客户id，查询是否推送UNI(主要用于面单)", description = "根据客户id，查询是否推送UNI(主要用于面单)")
    @Inner(value = false)
    @GetMapping("/selectIsPush/{customerId}")
    public String selectIsPush(@RequestParam("customerId") Long customerId) {
        return tmsCustomerService.selectIsPush(customerId);
    }



    /**
     * 导出excel 表格
     * @param tmsCustomer 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCustomer_export')" )
    public List<TmsCustomerEntity> export(TmsCustomerEntity tmsCustomer,Long[] ids) {
        return tmsCustomerService.getExcel(tmsCustomer, ids);
    }
}