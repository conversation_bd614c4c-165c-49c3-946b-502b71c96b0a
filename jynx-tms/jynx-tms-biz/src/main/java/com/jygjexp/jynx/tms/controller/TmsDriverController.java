package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.entity.TmsDriverEntity;

import com.jygjexp.jynx.tms.entity.TmsEntrustedOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsPollCodeEntity;
import com.jygjexp.jynx.tms.enums.EntrustedOrderStatus;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.TmsDriverLocationVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPageVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverDeliveryVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverPersonVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverVo;
import com.jygjexp.jynx.tms.vo.excel.TmsDriverExportVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡派司机信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:14:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriver" )
@Tag(description = "tmsDriver" , name = "卡派司机信息记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverController {

    private final  TmsDriverService tmsDriverService;
    private final TmsPollCodeService pollCodeService;
    private final TmsOrderTrackService orderTrackService;
    private final TmsEntrustedOrderService entrustedOrderService;
    private final TmsShipmentOrderService shipmentOrderService;
    private final TmsRoutePlanService tmsRoutePlanService;
    private final RemoteTmsAppUserService remoteTmsAppUserService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 司机信息记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    //@Debounce(timeout = 3000) // 3 秒防抖
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_view')" )
    public R getTmsDriverPage(@ParameterObject Page page, @ParameterObject TmsDriverPageVo vo) {
        return R.ok(tmsDriverService.search(page, vo));
    }


    /**
     * 通过id查询司机信息记录
     * @param driverId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{driverId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_view')" )
    public R getById(@PathVariable("driverId" ) Long driverId) {
        return R.ok(tmsDriverService.getById(driverId));
    }

    /**
     * 新增司机信息记录
     * @param tmsDriver 司机信息记录
     * @return R
     */
    @Operation(summary = "新增司机信息记录" , description = "新增司机信息记录" )
    @SysLog("新增司机信息记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_add')" )
    public R save(@RequestBody TmsDriverEntity tmsDriver) {
        return tmsDriverService.saveDriver(tmsDriver);
    }

    /**
     * 修改司机信息记录
     * @param tmsDriver 司机信息记录
     * @return R
     */
    @Operation(summary = "修改司机信息记录" , description = "修改司机信息记录" )
    @SysLog("修改司机信息记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_edit')" )
    public R updateById(@RequestBody TmsDriverEntity tmsDriver) {
        return tmsDriverService.updateDriver(tmsDriver);
    }

    /**
     * 通过id删除司机信息记录
     * @param ids driverId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机信息记录" , description = "通过id删除司机信息记录" )
    @SysLog("通过id删除司机信息记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_del')" )
    public R removeById(@RequestBody Long[] ids) {
        // 根据ids批量查询司机信息
        List<TmsDriverEntity> driverList = tmsDriverService.listByIds(CollUtil.toList(ids));
        for (TmsDriverEntity driver : driverList) {
            // 根据手机号查询系统后台用户账号
            R<AppUserInfo> info = remoteTmsAppUserService.info(driver.getPhone());
            if (info.getCode() == 0) {
                remoteTmsAppUserService.removeById(new Long[]{info.getData().getAppUser().getUserId()});
            }
        }
        return R.ok(tmsDriverService.removeBatchByIds(CollUtil.toList(ids)));
    }


    // 司机启用停用时，也需判断手机号是否存在
    @Operation(summary = "卡派司机启用停用" , description = "卡派司机启用停用" )
    @SysLog("卡派司机启用停用" )
    @PostMapping("/isEnableAndDisable")
    public R businessSwitch(@RequestParam Long driverId, @RequestParam Integer isValid) {
        return tmsDriverService.businessSwitch(driverId, isValid);
    }


    /**
     * 导出excel 表格
     * @param tmsDriver 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriver_export')" )
    public List<TmsDriverExportVo> export(TmsDriverPageVo tmsDriver, Long[] ids) {
        return tmsDriverService.getExcel(tmsDriver,ids);
    }

    /**
     * 司机列表
     * @return
     */
    @Operation(summary = "司机列表" , description = "司机列表" )
    @GetMapping("/listAllDriver")
    @Inner(value = false)
    public R<List<TmsDriverEntity>> listAllDriver(){
        return R.ok(tmsDriverService.list());
    }


    // 司机app相关操作-------------------------------------------------------------
    @Operation(summary = "司机app注册" , description = "司机app注册" )
    @SysLog("司机app注册" )
    @PostMapping("/app/register")
    public R register(@RequestBody TmsAppDriverVo tmsAppDriverVo) {
        return tmsDriverService.register(tmsAppDriverVo);
    }

    @Operation(summary = "司机app修改个人信息" , description = "司机app修改个人信息" )
    @SysLog("司机app修改个人信息" )
    @PostMapping("/app/driver/update")
    public R driverInfoUpdate(@RequestBody TmsAppDriverPersonVo driverInfo) {
        return tmsDriverService.driverInfoUpdate(driverInfo);
    }


    // 根据注册码获取承运商id
    @Operation(summary = "app-根据注册码获取承运商id" , description = "app-根据注册码获取承运商id" )
    @PostMapping("/app/register/code")
    public R registerCode(@RequestParam String pollCode) {
        TmsPollCodeEntity pollCodeEntity = pollCodeService.getOne(new LambdaQueryWrapper<TmsPollCodeEntity>()
                .eq(TmsPollCodeEntity::getPollCode, pollCode), false);
        if (null == pollCodeEntity) {
            return LocalizedR.failed("tms.app.driver.register.poll.code.error",pollCode);
        }
        return R.ok(pollCodeEntity.getCarrierId());
    }

    // 司机营业开关
    @Operation(summary = "app-司机营业开关" , description = "app-司机营业开关" )
    @GetMapping("/app/business/switch")
    public R businessSwitch(@RequestParam Long driverId, @RequestParam Boolean isBusiness) {
        TmsDriverEntity tmsDriverEntity = tmsDriverService.getById(driverId);
        tmsDriverEntity.setIsOpen(isBusiness);
        return R.ok(tmsDriverService.updateById(tmsDriverEntity));
    }

    // 根据手机号查询司机信息
    @Operation(summary = "app-根据手机号查询司机信息" , description = "app-根据手机号查询司机信息" )
    @GetMapping("/app/getPhone/{phone}")
    public R getPhone(@PathVariable("phone") String phone) {
        // 根据手机号查询司机信息
        TmsDriverEntity tmsDriverEntity = tmsDriverService.getOne(new LambdaQueryWrapper<TmsDriverEntity>()
                .eq(TmsDriverEntity::getPhone, phone)
                .eq(TmsDriverEntity::getIsValid, true), false);
        return R.ok(tmsDriverEntity);
    }

    // 司机扫码取货
    @Operation(summary = "app-司机扫描取货" , description = "app-司机扫描取货" )
    @GetMapping("/app/scan/pick/{entrustedOrderNo}/{driverId}")
    public R scanPick(@PathVariable("entrustedOrderNo") String entrustedOrderNo,@PathVariable("driverId") Long driverId) {
        return tmsDriverService.scanPick(entrustedOrderNo,driverId);
    }

    // 根据运输单查询取货送货列表
    @Operation(summary = "app-根据运输单查询取货送货列表" , description = "app-根据运输单查询取货送货列表" )
    @GetMapping("/app/pickList/{shipmentOrderNo}/{isSeanAndGet}")
    public R pickList(@PathVariable("shipmentOrderNo") String shipmentOrderNo,@PathVariable("isSeanAndGet") Boolean isSeanAndGet) {
        return tmsDriverService.getPickupList(shipmentOrderNo,isSeanAndGet);
    }

    // 司机上传取货证明
    @Operation(summary = "app-司机上传取货证明" , description = "app-司机上传取货证明" )
    @PostMapping("/app/upload/pickup/proof")
    public R uploadPickupProof(@RequestParam String entrustedOrderNumber,@RequestParam String pickupProof) {
        return tmsDriverService.uploadPickupProof(entrustedOrderNumber,pickupProof);
    }

    // 司机送货成功
    @Operation(summary = "app-司机送货成功" , description = "app-司机送货成功" )
    @PostMapping("/app/delivery/success")
    public R deliverySuccess(@Valid @RequestBody TmsAppDriverDeliveryVo vo) {
        return tmsDriverService.deliverySuccess(vo.getEntrustedOrderNumber(),vo.getPickupProof(),vo.getDeliveryProof());
    }


    // 司机送货失败
    @Operation(summary = "app-司机送货失败" , description = "app-司机送货失败" )
    @GetMapping("/app/delivery/failed")
    public R deliveryFailed(@RequestParam String entrustedOrderNumber) {
        return tmsDriverService.deliveryFailed(entrustedOrderNumber);
    }


    // 根据委托单号查询委托单信息
    @Operation(summary = "app-根据委托单号查询委托单信息" , description = "app-根据委托单号查询委托单信息" )
    @GetMapping("/app/getEntrustedOrder/{entrustedOrderNumber}")
    public R getEntrustedOrder(@PathVariable("entrustedOrderNumber") String entrustedOrderNumber) {
        return tmsDriverService.getEntrustedOrder(entrustedOrderNumber);
    }

    // 实时存储司机经纬度
    @Operation(summary = "app-实时存储司机经纬度" , description = "app-实时存储司机经纬度" )
    @PostMapping("/app/driver/location")
    public R driverLocation(@RequestBody TmsDriverLocationVo driverLocationVo) {
        return tmsDriverService.driverLocation(driverLocationVo);
    }

    // 获取空闲司机实时经纬度
    @Operation(summary = "app-获取空闲司机实时经纬度" , description = "app-获取空闲司机实时经纬度" )
    @GetMapping("/app/get/driver/location")
    public R getDriverLocation() {
        return tmsDriverService.getDriverLocation();
    }

    // 客户拒签
    @Operation(summary = "app-客户拒签" , description = "app-客户拒签" )
    @PostMapping("/app/customer/refuse")
    public R customerRefuse(@RequestParam String entrustedOrderNumber,@RequestParam String refuseReasons) {
        return tmsDriverService.customerRefuse(entrustedOrderNumber,refuseReasons);
    }

}