package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.RequestExcel;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.tms.ebo.TmsDriverSignEbo;
import com.jygjexp.jynx.tms.entity.TmsDriverSignEntity;
import com.jygjexp.jynx.tms.model.bo.QueryCondition;
import com.jygjexp.jynx.tms.qbo.TmsDriverSignQueryBo;
import com.jygjexp.jynx.tms.service.TmsDriverSignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 司机签到表
 *
 * <AUTHOR>
 * @date 2025-07-15 11:21:20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverSign" )
@Tag(description = "tmsDriverSign" , name = "司机签到表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverSignController {

    private final TmsDriverSignService tmsDriverSignService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R<IPage<TmsDriverSignEntity>> getTmsDriverSignPage(@RequestBody QueryCondition<TmsDriverSignQueryBo> qbo) {
        return R.ok(tmsDriverSignService.pageData(qbo));
    }

    /**
     * 新增
     */
    @Operation(summary = "新增" , description = "新增" )
    @PostMapping("addData")
    public R<Boolean> addData(@RequestBody TmsDriverSignEntity tmsDriverSign){
        return R.ok(tmsDriverSignService.save(tmsDriverSign));
    }


    /**
     * 导出
     */
    @ResponseExcel
    @PostMapping("/export")
    public List<TmsDriverSignEbo> export(@RequestBody QueryCondition<TmsDriverSignQueryBo> qbo) {
        return tmsDriverSignService.exportData(qbo);
    }


    /**
     * 导入
     */
    @PostMapping("/import")
    public R importData(@RequestExcel @Valid List<TmsDriverSignEbo> ebos) {
        tmsDriverSignService.importData(ebos);
        return R.ok();
    }

    /**
     * 提醒打卡
     */
    @Operation(summary = "提醒打卡" , description = "提醒打卡" )
    @GetMapping("/remind/{num}")
    public R remind(@PathVariable String num) {
        Boolean remind = tmsDriverSignService.remind(num);
        return R.ok(remind);
    }
}