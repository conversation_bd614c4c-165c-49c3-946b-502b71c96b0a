package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.vo.TmsEntrustedOrderPageVo;
import groovy.util.logging.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: 异常订单重推小包
 * @Date: 2025/6/27 11:08
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsExOrderPushXb")
@Tag(description = "异常订单重推小包" , name = "异常订单重推小包" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsExOrderPushXbController {

    private final TmsCustomerOrderService tmsCustomerOrderService;

    /**
     * 异常订单重新推送
     * @param subOrderNoList 跟踪单箱号
     * @return R
     */
    @Operation(summary = "异常订单重新推送小包",description = "异常订单重新推送小包")
    @PostMapping("/push")
    @PreAuthorize("@pms.hasPermission('tms_exorder_push')")
    public R push(@RequestBody List<String> subOrderNoList) {
        return tmsCustomerOrderService.exOrderPushXb(subOrderNoList);
    }


}
