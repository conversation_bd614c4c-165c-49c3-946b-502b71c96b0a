package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleExprEntity;
import com.jygjexp.jynx.tms.service.TmsFeeRuleExprService;
import com.jygjexp.jynx.tms.service.TmsFeeRuleService;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleDetailVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 附加费表达式表
 *
 * <AUTHOR>
 * @date 2025-07-14 15:12:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsFeeRuleExpr" )
@Tag(description = "tmsFeeRuleExpr" , name = "附加费表达式表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsFeeRuleExprController {

    private final  TmsFeeRuleExprService tmsFeeRuleExprService;
    private final  TmsFeeRuleService tmsFeeRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsFeeRuleExpr 附加费表达式表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_view')" )
    public R getTmsFeeRuleExprPage(@ParameterObject Page page, @ParameterObject TmsFeeRuleExprEntity tmsFeeRuleExpr) {
        LambdaQueryWrapper<TmsFeeRuleExprEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsFeeRuleExprService.page(page, wrapper));
    }


    /**
     * 通过id查询附加费详情信息
     * @param id 附加费ID
     * @return R 包含附加费基本信息和表达式规则的完整数据
     */
    @Operation(summary = "通过id查询附加费详情" , description = "查询附加费基本信息和关联的表达式规则" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        if (id == null) {
            return R.failed("ID不能为空");
        }

        TmsFeeRuleDetailVo detailVo = tmsFeeRuleService.getFeeRuleDetailById(id);
        if (detailVo == null) {
            return R.failed("未找到对应的附加费信息");
        }

        return R.ok(detailVo);
    }

    /**
     * 新增附加费表达式表
     * @param tmsFeeRuleExpr 附加费表达式表
     * @return R
     */
    @Operation(summary = "新增附加费表达式表" , description = "新增附加费表达式表" )
    @SysLog("新增附加费表达式表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_add')" )
    public R save(@RequestBody TmsFeeRuleExprEntity tmsFeeRuleExpr) {
        return R.ok(tmsFeeRuleExprService.save(tmsFeeRuleExpr));
    }

    /**
     * 修改附加费表达式表
     * @param tmsFeeRuleExpr 附加费表达式表
     * @return R
     */
    @Operation(summary = "修改附加费表达式表" , description = "修改附加费表达式表" )
    @SysLog("修改附加费表达式表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_edit')" )
    public R updateById(@RequestBody TmsFeeRuleExprEntity tmsFeeRuleExpr) {
        return R.ok(tmsFeeRuleExprService.updateById(tmsFeeRuleExpr));
    }

    /**
     * 通过id删除附加费表达式表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除附加费表达式表" , description = "通过id删除附加费表达式表" )
    @SysLog("通过id删除附加费表达式表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsFeeRuleExprService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsFeeRuleExpr 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRuleExpr_export')" )
    public List<TmsFeeRuleExprEntity> export(TmsFeeRuleExprEntity tmsFeeRuleExpr,Long[] ids) {
        return tmsFeeRuleExprService.list(Wrappers.lambdaQuery(tmsFeeRuleExpr).in(ArrayUtil.isNotEmpty(ids), TmsFeeRuleExprEntity::getId, ids));
    }
}