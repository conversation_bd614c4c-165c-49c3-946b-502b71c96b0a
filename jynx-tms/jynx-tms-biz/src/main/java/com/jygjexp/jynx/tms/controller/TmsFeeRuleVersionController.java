package com.jygjexp.jynx.tms.controller;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.service.TmsFeeRuleService;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleDetailVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleUpdateVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionCompareVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附加费版本管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsFeeRule/version")
@Tag(description = "tmsFeeRuleVersion", name = "附加费版本管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsFeeRuleVersionController {

    private final TmsFeeRuleService tmsFeeRuleService;

    /**
     * 创建新版本
     */
    @PostMapping("/create")
    @Operation(summary = "创建新版本", description = "基于现有版本创建新的附加费规则版本")
    @SysLog("创建附加费新版本")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_edit')")
    public R<Boolean> createNewVersion(@RequestBody TmsFeeRuleUpdateVo updateVo, 
                                       @RequestParam(required = false, defaultValue = "系统版本更新") String versionDescription) {
        try {
            boolean result = tmsFeeRuleService.createNewVersion(updateVo, versionDescription);
            if (result) {
                return R.ok(true, "新版本创建成功");
            } else {
                return R.failed("新版本创建失败，请检查输入数据");
            }
        } catch (Exception e) {
            return R.failed("新版本创建异常：" + e.getMessage());
        }
    }

    /**
     * 查询指定费用规则的所有版本
     */
    @GetMapping("/list/{originalFeeRuleId}")
    @Operation(summary = "查询版本列表", description = "查询指定费用规则的所有历史版本")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')")
    public R<List<TmsFeeRuleVersionVo>> getFeeRuleVersions(@PathVariable Long originalFeeRuleId) {
        try {
            List<TmsFeeRuleVersionVo> versions = tmsFeeRuleService.getFeeRuleVersions(originalFeeRuleId);
            return R.ok(versions);
        } catch (Exception e) {
            return R.failed("查询版本列表异常：" + e.getMessage());
        }
    }

    /**
     * 获取当前生效版本
     */
    @GetMapping("/current/{originalFeeRuleId}")
    @Operation(summary = "获取当前版本", description = "获取指定费用规则的当前生效版本")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')")
    public R<TmsFeeRuleDetailVo> getCurrentVersion(@PathVariable Long originalFeeRuleId) {
        try {
            TmsFeeRuleDetailVo currentVersion = tmsFeeRuleService.getCurrentVersion(originalFeeRuleId);
            if (currentVersion != null) {
                return R.ok(currentVersion);
            } else {
                return R.failed("未找到当前生效版本");
            }
        } catch (Exception e) {
            return R.failed("获取当前版本异常：" + e.getMessage());
        }
    }

    /**
     * 版本对比
     */
    @GetMapping("/compare")
    @Operation(summary = "版本对比", description = "对比两个版本之间的差异")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')")
    public R<TmsFeeRuleVersionCompareVo> compareVersions(@RequestParam Long oldVersionId, 
                                                         @RequestParam Long newVersionId) {
        try {
            if (oldVersionId == null || newVersionId == null) {
                return R.failed("版本ID不能为空");
            }
            
            TmsFeeRuleVersionCompareVo compareResult = tmsFeeRuleService.compareVersions(oldVersionId, newVersionId);
            if (compareResult != null) {
                return R.ok(compareResult);
            } else {
                return R.failed("版本对比失败，请检查版本ID是否正确");
            }
        } catch (Exception e) {
            return R.failed("版本对比异常：" + e.getMessage());
        }
    }

    /**
     * 激活指定版本
     */
    @PutMapping("/activate/{versionId}")
    @Operation(summary = "激活版本", description = "将指定版本设置为当前生效版本")
    @SysLog("激活附加费版本")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_edit')")
    public R<Boolean> activateVersion(@PathVariable Long versionId) {
        try {
            if (versionId == null) {
                return R.failed("版本ID不能为空");
            }
            
            boolean result = tmsFeeRuleService.activateVersion(versionId);
            if (result) {
                return R.ok(true, "版本激活成功");
            } else {
                return R.failed("版本激活失败，请检查版本是否存在");
            }
        } catch (Exception e) {
            return R.failed("版本激活异常：" + e.getMessage());
        }
    }

    /**
     * 查询版本详情
     */
    @GetMapping("/detail/{versionId}")
    @Operation(summary = "查询版本详情", description = "查询指定版本的详细信息")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')")
    public R<TmsFeeRuleDetailVo> getVersionDetail(@PathVariable Long versionId) {
        try {
            if (versionId == null) {
                return R.failed("版本ID不能为空");
            }
            
            TmsFeeRuleDetailVo versionDetail = tmsFeeRuleService.getFeeRuleDetailById(versionId);
            if (versionDetail != null) {
                return R.ok(versionDetail);
            } else {
                return R.failed("未找到指定版本的信息");
            }
        } catch (Exception e) {
            return R.failed("查询版本详情异常：" + e.getMessage());
        }
    }
}
