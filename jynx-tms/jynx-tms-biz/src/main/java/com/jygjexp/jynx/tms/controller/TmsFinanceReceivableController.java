package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivablePageDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStoreDetailsDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStorePageDto;
import com.jygjexp.jynx.tms.excel.TmsFinanceReceivableExcelDto;
import com.jygjexp.jynx.tms.excel.TmsFinanceReceivableStoreExcelDto;
import com.jygjexp.jynx.tms.service.TmsFinanceReceivableService;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivableStorePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * @Description
 * @Date 2025/8/5 11:33
 * @Created guqingren
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsFinanceReceivable")
@Tag(description = "tmsFinanceReceivable", name = "应收应付")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsFinanceReceivableController {

    private final TmsFinanceReceivableService tmsFinanceReceivableService;

    /**
     * 管理端-分页
     *
     * @param vo
     * @return
     */
    @Operation(summary = "管理端-分页")
    @PostMapping("/search")
    public R<Page<TmsFinanceReceivablePageDto>> search(@RequestBody TmsFinanceReceivablePageVo vo) {
        return R.ok(tmsFinanceReceivableService.search(vo));
    }

    /**
     * 管理端-详情
     *
     * @param storeId
     * @return
     */
    @Operation(summary = "管理端-详情")
    @PostMapping("/details/{storeId}")
    public R<TmsFinanceReceivablePageDto> details(@PathVariable Long storeId, @RequestParam String billingCycle) {
        return R.ok(tmsFinanceReceivableService.details(storeId, billingCycle));
    }

    /**
     * 管理端-详情分页
     *
     * @param storeId
     * @return
     */
    @Operation(summary = "管理端-详情分页")
    @PostMapping("/details/page/{storeId}")
    public R<Page<TmsFinanceReceivableStorePageDto>> detailsPage(@PathVariable Long storeId, @RequestBody HashMap<String, String> params) {
        String current = params.get("current");
        String size = params.get("size");
        String billingCycle = params.get("billingCycle");
        return R.ok(tmsFinanceReceivableService.detailsPage(storeId, Integer.valueOf(current), Integer.valueOf(size), billingCycle));
    }

    /**
     * 管理端-分页
     *
     * @param vo
     * @return
     */
    @ResponseExcel
    @Operation(summary = "管理端-导出")
    @PostMapping("/export")
    public List<TmsFinanceReceivableExcelDto> export(@RequestBody TmsFinanceReceivablePageVo vo) {
        return tmsFinanceReceivableService.export(vo);
    }

    /**
     * 门店端分页
     *
     * @param vo
     * @return
     */
    @Operation(summary = "门店端-分页")
    @PostMapping("/store/search")
    public R<Page<TmsFinanceReceivableStorePageDto>> storeSearch(@RequestBody TmsFinanceReceivableStorePageVo vo) {
        return R.ok(tmsFinanceReceivableService.storeSearch(vo));
    }

    /**
     * 门店端-详情
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "门店端-详情")
    @GetMapping("/store/details/{orderId}")
    public R<TmsFinanceReceivableStoreDetailsDto> storeDetails(@PathVariable Long orderId) {
        return R.ok(tmsFinanceReceivableService.storeDetails(orderId));
    }

    /**
     * 门店端-导出
     *
     * @param vo
     * @return
     */
    @ResponseExcel(i18nHeader = true)
    @Operation(summary = "门店端-导出")
    @PostMapping("/store/export")
    public List<TmsFinanceReceivableStoreExcelDto> storeExport(@RequestBody TmsFinanceReceivableStorePageVo vo) {
        return tmsFinanceReceivableService.storeExport(vo);
    }
}
