package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsCustomerOrderDto;
import com.jygjexp.jynx.tms.dto.TransferLineHaulOrderDto;
import com.jygjexp.jynx.tms.entity.TmsLineHaulOrderEntity;
import com.jygjexp.jynx.tms.service.TmsLineHaulOrderService;
import com.jygjexp.jynx.tms.vo.TmsLineHaulOrderAddVo;
import com.jygjexp.jynx.tms.vo.TmsLineHaulOrderPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsLineHaulOrderExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 干线任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 18:36:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsLineHaulOrder" )
@Tag(description = "tmsLineHaulOrder" , name = "干线任务单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsLineHaulOrderController {

    private final  TmsLineHaulOrderService tmsLineHaulOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 干线任务单
     * @return
     */
    @Operation(summary = "干线任务单分页查询" , description = "干线任务单分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_view')" )
    public R getTmsLineHaulOrderPage(@ParameterObject Page page, @ParameterObject TmsLineHaulOrderPageVo vo) {
        return R.ok(tmsLineHaulOrderService.search(page, vo));
    }


    /**
     * WEB管理端干线任务上传提货和送货证明
     * @param lineHaulOrderNo pickupProof WEB管理端干线任务上传提货和送货证明
     * @return R
     */
    @Operation(summary = "WEB管理端干线任务上传提货和送货证明" , description = "WEB管理端干线任务上传提货和送货证明" )
    @SysLog("WEB管理端干线任务上传提货和送货证明" )
    @PostMapping("/uploadLineProof")
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_uploadPf')" )
    public R uploadLineProof(@RequestParam String lineHaulOrderNo, @RequestParam String pickupProof,@RequestParam String deliveryProof) {
        return tmsLineHaulOrderService.uploadLineProof(lineHaulOrderNo, pickupProof,deliveryProof);
    }

    /**
     * 干线任务转单
     */
    @Operation(summary = "干线任务转单" , description = "干线任务转单" )
    @PostMapping("/transferLineHaulOrder")
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_transfer')" )
    public R transferLineHaulOrder(@RequestBody TransferLineHaulOrderDto transferLineHaulOrderDto) {
        return tmsLineHaulOrderService.transferLineHaulOrder(transferLineHaulOrderDto);
    }


    /**
     * 根据id查询干线详情
     * @param id id
     * @return R
     */
    @Operation(summary = "根据id查询干线详情" , description = "根据id查询干线详情" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return tmsLineHaulOrderService.selectById(id);
    }

    /**
     * 新增干线任务单
     * @param order 干线任务单
     * @return R
     */
    @Operation(summary = "新增干线任务单" , description = "新增干线任务单" )
    @SysLog("新增干线任务单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_add')" )
    public R save(@RequestBody TmsLineHaulOrderAddVo order) {
        return R.ok(tmsLineHaulOrderService.saveManyLineHaulOrder(order));
    }

//    /**
//     * 新增多个干线任务单
//     * @param order 干线任务单
//     * @return R
//     */
//    @Operation(summary = "新增多个干线任务单" , description = "新增多个干线任务单" )
//    @SysLog("新增多个干线任务单" )
//    @PostMapping("/saveManyLineHaulOrder")
//    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_batchAdd')" )
//    public R saveManyLineHaulOrder(@RequestBody TmsLineHaulOrderAddVo order) {
//        return R.ok(tmsLineHaulOrderService.saveManyLineHaulOrder(order));
//    }

    @Operation(summary = "干线任务跟踪单列表" , description = "干线任务跟踪单列表" )
    @GetMapping("/listCustomerOrder" )
    @Inner(value = false)
    public R listCustomerOrder(@ParameterObject Page page, @ParameterObject TmsCustomerOrderDto vo) {
        return R.ok(tmsLineHaulOrderService.listCustomerOrder(page, vo));
    }

    @Operation(summary = "取消干线任务" , description = "取消干线任务" )
    @PostMapping("/cancelLineHaulOrder" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_cancel')" )
    public R cancelLineHaulOrder(@RequestBody List<Long> ids) {
        return R.ok(tmsLineHaulOrderService.cancelLineHaulOrder(ids));
    }

    /**
     * 修改干线任务单
     * @param tmsLineHaulOrder 干线任务单
     * @return R
     */
    @Operation(summary = "修改干线任务单" , description = "修改干线任务单" )
    @SysLog("修改干线任务单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_edit')" )
    public R updateById(@RequestBody TmsLineHaulOrderAddVo tmsLineHaulOrder) {
        return R.ok(tmsLineHaulOrderService.updateLineOrderById(tmsLineHaulOrder));
    }

    /**
     * 通过id删除干线任务单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除干线任务单" , description = "通过id删除干线任务单" )
    @SysLog("通过id删除干线任务单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsLineHaulOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @Inner(value = false)
//    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrder_export')" )
    public List<TmsLineHaulOrderExcelVo> export(@RequestParam(required = false) TmsLineHaulOrderPageVo vo) {
        return tmsLineHaulOrderService.export(vo);
    }
}