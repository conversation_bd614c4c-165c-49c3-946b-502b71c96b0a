package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;

import com.jygjexp.jynx.tms.dto.app.TmsMessageDto;
import com.jygjexp.jynx.tms.entity.TmsExceptionManagementEntity;
import com.jygjexp.jynx.tms.entity.TmsMessageEntity;
import com.jygjexp.jynx.tms.service.TmsMessageService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TMS消息
 *
 * <AUTHOR>
 * @date 2025-03-05 10:02:20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsMessage" )
@Tag(description = "tmsMessage" , name = "TMS消息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsMessageController {

    private final  TmsMessageService tmsMessageService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsMessage TMS消息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_view')" )
    public R getTmsMessagePage(@ParameterObject Page page, @ParameterObject TmsMessageDto tmsMessage) {
        return R.ok(tmsMessageService.search(page, tmsMessage));
    }


    /**
     * 通过id查询TMS消息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(tmsMessageService.getById(id));
    }

    /**
     * 新增TMS消息
     * @param tmsMessage TMS消息
     * @return R
     */
    @Operation(summary = "新增TMS消息" , description = "新增TMS消息" )
    @SysLog("新增TMS消息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_add')" )
    public R save(@RequestBody TmsMessageEntity tmsMessage) {
        return R.ok(tmsMessageService.save(tmsMessage));
    }

    /**
     * 修改TMS消息
     * @param tmsMessage TMS消息
     * @return R
     */
    @Operation(summary = "修改TMS消息" , description = "修改TMS消息" )
    @SysLog("修改TMS消息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_edit')" )
    public R updateById(@RequestBody TmsMessageEntity tmsMessage) {
        return R.ok(tmsMessageService.updateById(tmsMessage));
    }

    /**
     * 通过id删除TMS消息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除TMS消息" , description = "通过id删除TMS消息" )
    @SysLog("通过id删除TMS消息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(tmsMessageService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsMessage 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsMessage_export')" )
    public List<TmsMessageEntity> export(TmsMessageEntity tmsMessage,Integer[] ids) {
        return tmsMessageService.list(Wrappers.lambdaQuery(tmsMessage).in(ArrayUtil.isNotEmpty(ids), TmsMessageEntity::getId, ids));
    }
}