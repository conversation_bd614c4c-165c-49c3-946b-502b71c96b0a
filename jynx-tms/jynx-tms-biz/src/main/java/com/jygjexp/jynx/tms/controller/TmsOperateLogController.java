package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsOperateLogEntity;
import com.jygjexp.jynx.tms.model.bo.QueryCondition;
import com.jygjexp.jynx.tms.qbo.TmsOperateLogQbo;
import com.jygjexp.jynx.tms.service.TmsOperateLogService;
import com.jygjexp.jynx.tms.vo.TmsOperateLogVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * tms业务操作日志
 *
 * <AUTHOR>
 * @date 2025-07-21 17:33:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOperateLog" )
@Tag(description = "tmsOperateLog" , name = "tms业务操作日志管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOperateLogController {

    private final  TmsOperateLogService tmsOperateLogService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R<IPage<TmsOperateLogVo>> getTmsOperateLogPage(@RequestBody QueryCondition<TmsOperateLogQbo> qbo) {
        return R.ok(tmsOperateLogService.pageData(qbo));
    }


}