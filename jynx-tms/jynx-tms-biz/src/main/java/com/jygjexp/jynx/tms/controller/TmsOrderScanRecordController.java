package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsOrderScanRecordEntity;
import com.jygjexp.jynx.tms.service.TmsOrderScanRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机扫描订单记录表
 *
 * <AUTHOR>
 * @date 2025-07-03 14:25:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOrderScanRecord" )
@Tag(description = "tmsOrderScanRecord" , name = "司机扫描订单记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOrderScanRecordController {

    private final  TmsOrderScanRecordService tmsOrderScanRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsOrderScanRecord 司机扫描订单记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_view')" )
    public R getTmsOrderScanRecordPage(@ParameterObject Page page, @ParameterObject TmsOrderScanRecordEntity tmsOrderScanRecord) {
        LambdaQueryWrapper<TmsOrderScanRecordEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsOrderScanRecordService.page(page, wrapper));
    }


    /**
     * 通过id查询司机扫描订单记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsOrderScanRecordService.getById(id));
    }

    /**
     * 新增司机扫描订单记录表
     * @param tmsOrderScanRecord 司机扫描订单记录表
     * @return R
     */
    @Operation(summary = "新增司机扫描订单记录表" , description = "新增司机扫描订单记录表" )
    @SysLog("新增司机扫描订单记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_add')" )
    public R save(@RequestBody TmsOrderScanRecordEntity tmsOrderScanRecord) {
        return R.ok(tmsOrderScanRecordService.save(tmsOrderScanRecord));
    }

    /**
     * 修改司机扫描订单记录表
     * @param tmsOrderScanRecord 司机扫描订单记录表
     * @return R
     */
    @Operation(summary = "修改司机扫描订单记录表" , description = "修改司机扫描订单记录表" )
    @SysLog("修改司机扫描订单记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_edit')" )
    public R updateById(@RequestBody TmsOrderScanRecordEntity tmsOrderScanRecord) {
        return R.ok(tmsOrderScanRecordService.updateById(tmsOrderScanRecord));
    }

    /**
     * 通过id删除司机扫描订单记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除司机扫描订单记录表" , description = "通过id删除司机扫描订单记录表" )
    @SysLog("通过id删除司机扫描订单记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsOrderScanRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsOrderScanRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderScanRecord_export')" )
    public List<TmsOrderScanRecordEntity> export(TmsOrderScanRecordEntity tmsOrderScanRecord,Long[] ids) {
        return tmsOrderScanRecordService.list(Wrappers.lambdaQuery(tmsOrderScanRecord).in(ArrayUtil.isNotEmpty(ids), TmsOrderScanRecordEntity::getId, ids));
    }
}