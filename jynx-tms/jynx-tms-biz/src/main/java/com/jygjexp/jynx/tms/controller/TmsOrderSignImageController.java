package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsOrderSignImageEntity;
import com.jygjexp.jynx.tms.service.TmsOrderSignImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 卡派 签收照片
 *
 * <AUTHOR>
 * @date 2025-02-12 18:23:01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOrderSignImage" )
@Tag(description = "tmsOrderSignImage" , name = "卡派 签收照片管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOrderSignImageController {

    private final  TmsOrderSignImageService tmsOrderSignImageService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsOrderSignImage 卡派 签收照片
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_view')" )
    public R getTmsOrderSignImagePage(@ParameterObject Page page, @ParameterObject TmsOrderSignImageEntity tmsOrderSignImage) {
        LambdaQueryWrapper<TmsOrderSignImageEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsOrderSignImageService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派 签收照片
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsOrderSignImageService.getById(id));
    }

    /**
     * 新增卡派 签收照片
     * @param tmsOrderSignImage 卡派 签收照片
     * @return R
     */
    @Operation(summary = "新增卡派 签收照片" , description = "新增卡派 签收照片" )
    @SysLog("新增卡派 签收照片" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_add')" )
    public R save(@RequestBody TmsOrderSignImageEntity tmsOrderSignImage) {
        return R.ok(tmsOrderSignImageService.save(tmsOrderSignImage));
    }

    /**
     * 修改卡派 签收照片
     * @param tmsOrderSignImage 卡派 签收照片
     * @return R
     */
    @Operation(summary = "修改卡派 签收照片" , description = "修改卡派 签收照片" )
    @SysLog("修改卡派 签收照片" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_edit')" )
    public R updateById(@RequestBody TmsOrderSignImageEntity tmsOrderSignImage) {
        return R.ok(tmsOrderSignImageService.updateById(tmsOrderSignImage));
    }

    /**
     * 通过id删除卡派 签收照片
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派 签收照片" , description = "通过id删除卡派 签收照片" )
    @SysLog("通过id删除卡派 签收照片" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsOrderSignImageService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsOrderSignImage 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_tmsOrderSignImage_export')" )
    public List<TmsOrderSignImageEntity> export(TmsOrderSignImageEntity tmsOrderSignImage,Long[] ids) {
        return tmsOrderSignImageService.list(Wrappers.lambdaQuery(tmsOrderSignImage).in(ArrayUtil.isNotEmpty(ids), TmsOrderSignImageEntity::getId, ids));
    }
}