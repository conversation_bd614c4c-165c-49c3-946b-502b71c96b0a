package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsOrderStatisticsEntity;
import com.jygjexp.jynx.tms.service.TmsOrderStatisticsService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 中大件派送订单统计表
 *
 * <AUTHOR>
 * @date 2025-06-25 19:01:48
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOrderStatistics" )
@Tag(description = "tmsOrderStatistics" , name = "中大件派送订单统计表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOrderStatisticsController {

    private final  TmsOrderStatisticsService tmsOrderStatisticsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsOrderStatistics 中大件派送订单统计表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_view')" )
    public R getTmsOrderStatisticsPage(@ParameterObject Page page, @ParameterObject TmsOrderStatisticsEntity tmsOrderStatistics) {
        LambdaQueryWrapper<TmsOrderStatisticsEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsOrderStatisticsService.page(page, wrapper));
    }


    /**
     * 通过id查询中大件派送订单统计表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsOrderStatisticsService.getById(id));
    }

    /**
     * 新增中大件派送订单统计表
     * @param tmsOrderStatistics 中大件派送订单统计表
     * @return R
     */
    @Operation(summary = "新增中大件派送订单统计表" , description = "新增中大件派送订单统计表" )
    @SysLog("新增中大件派送订单统计表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_add')" )
    public R save(@RequestBody TmsOrderStatisticsEntity tmsOrderStatistics) {
        return R.ok(tmsOrderStatisticsService.save(tmsOrderStatistics));
    }

    /**
     * 修改中大件派送订单统计表
     * @param tmsOrderStatistics 中大件派送订单统计表
     * @return R
     */
    @Operation(summary = "修改中大件派送订单统计表" , description = "修改中大件派送订单统计表" )
    @SysLog("修改中大件派送订单统计表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_edit')" )
    public R updateById(@RequestBody TmsOrderStatisticsEntity tmsOrderStatistics) {
        return R.ok(tmsOrderStatisticsService.updateById(tmsOrderStatistics));
    }

    /**
     * 通过id删除中大件派送订单统计表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件派送订单统计表" , description = "通过id删除中大件派送订单统计表" )
    @SysLog("通过id删除中大件派送订单统计表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsOrderStatisticsService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsOrderStatistics 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderStatistics_export')" )
    public List<TmsOrderStatisticsEntity> export(TmsOrderStatisticsEntity tmsOrderStatistics,Long[] ids) {
        return tmsOrderStatisticsService.list(Wrappers.lambdaQuery(tmsOrderStatistics).in(ArrayUtil.isNotEmpty(ids), TmsOrderStatisticsEntity::getId, ids));
    }
}