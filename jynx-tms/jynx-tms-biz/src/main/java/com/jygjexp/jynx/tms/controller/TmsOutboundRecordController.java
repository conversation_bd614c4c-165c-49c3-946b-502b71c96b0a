package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsOutboundRecordDto;
import com.jygjexp.jynx.tms.dto.TmsOutboundToRecordStorageDto;
import com.jygjexp.jynx.tms.entity.TmsOutboundRecordEntity;
import com.jygjexp.jynx.tms.service.TmsOutboundRecordService;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsOutboundRecordPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 卡派出库记录
 *
 * <AUTHOR>
 * @date 2025-04-03 14:22:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOutboundRecord" )
@Tag(description = "tmsOutboundRecord" , name = "卡派出库记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOutboundRecordController {

    private final  TmsOutboundRecordService tmsOutboundRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 卡派出库记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_view')" )
    public R getTmsOutboundRecordPage(Page page,TmsOutboundRecordPageVo vo) {
        return R.ok(tmsOutboundRecordService.search(page, vo));
    }

//    /**
//     * 查询已入库的跟踪单列表
//     * @param
//     * @return R
//     */
//    @Operation(summary = "查询已入库的跟踪单列表" , description = "查询已入库的跟踪单列表" )
//    @PostMapping("/getAllStorageOrder" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_view')" )
//    public R getAllStorageOrder(@RequestBody TmsOutboundToRecordStorageDto tmsOutboundToRecordStorageDto) {
//        return R.ok(tmsOutboundRecordService.getAllStorageOrder(tmsOutboundToRecordStorageDto));
//    }

    /**
     * 查询已入库的跟踪单列表-新
     * @param
     * @return R
     */
    @Operation(summary = "跟踪单列表查询", description = "跟踪单列表查询")
    @GetMapping("/getAllStorageOrderNew")
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_view')")
    public R getAllStorageOrderNew(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo vo) {
        return R.ok(tmsOutboundRecordService.getAllStorageOrderNew(page, vo));
    }


    /**
     * 通过id查询卡派出库记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsOutboundRecordService.getById(id));
    }

    /**
     * 新增卡派出库记录
     * @param tmsOutboundRecordDto 卡派出库记录
     * @return R
     */
    @Operation(summary = "新增卡派出库记录" , description = "新增卡派出库记录" )
    @SysLog("新增卡派出库记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_add')" )
    public R save(@RequestBody TmsOutboundRecordDto tmsOutboundRecordDto) {
        return R.ok(tmsOutboundRecordService.saveOutboundRecord(tmsOutboundRecordDto));
    }

    /**
     * 修改卡派出库记录
     * @param tmsOutboundRecord 卡派出库记录
     * @return R
     */
    @Operation(summary = "修改卡派出库记录" , description = "修改卡派出库记录" )
    @SysLog("修改卡派出库记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_edit')" )
    public R updateById(@RequestBody TmsOutboundRecordEntity tmsOutboundRecord) {
        return R.ok(tmsOutboundRecordService.updateById(tmsOutboundRecord));
    }

    /**
     * 通过id删除卡派出库记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派出库记录" , description = "通过id删除卡派出库记录" )
    @SysLog("通过id删除卡派出库记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_del')" )
    public R removeById(@RequestBody List<Long> ids) {
        return R.ok(tmsOutboundRecordService.removeBatchByIds(ids));
    }


    /**
     * 导出excel 表格
     * @param tmsOutboundRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOutboundRecord_export')" )
    public List<TmsOutboundRecordEntity> export(TmsOutboundRecordEntity tmsOutboundRecord,Long[] ids) {
        return tmsOutboundRecordService.list(Wrappers.lambdaQuery(tmsOutboundRecord).in(ArrayUtil.isNotEmpty(ids), TmsOutboundRecordEntity::getId, ids));
    }
}