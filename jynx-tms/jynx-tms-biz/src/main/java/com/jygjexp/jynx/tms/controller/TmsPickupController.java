package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.vo.TmsPickupPageVo;
import com.jygjexp.jynx.tms.service.TmsPickupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Optional;

/**
 * 卡派自提信息表
 *
 * <AUTHOR>
 * @date 2025-02-11 15:31:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsPickup" )
@Tag(description = "tmsPickup" , name = "卡派自提信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPickupController {

    private final  TmsPickupService tmsPickupService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 卡派自提信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_view')" )
    public R getTmsPickupPage(@ParameterObject Page page, @ParameterObject TmsPickupPageVo vo) {
        return R.ok(tmsPickupService.search(page, vo));
    }


    /**
     * 通过id查询卡派自提信息表
     * @param pickupId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pickupId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_view')" )
    public R getById(@PathVariable("pickupId" ) Long pickupId) {
        return R.ok(tmsPickupService.getById(pickupId));
    }

    /**
     * 新增卡派自提信息表
     * @param tmsPickup 卡派自提信息表
     * @return R
     */
    @Operation(summary = "新增卡派自提信息表" , description = "新增卡派自提信息表" )
    @SysLog("新增卡派自提信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_add')" )
    public R save(@RequestBody TmsPickupEntity tmsPickup) {
        return R.ok(tmsPickupService.save(tmsPickup));
    }

    /**
     * 修改卡派自提信息表
     * @param tmsPickup 卡派自提信息表
     * @return R
     */
    @Operation(summary = "修改卡派自提信息表" , description = "修改卡派自提信息表" )
    @SysLog("修改卡派自提信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_edit')" )
    public R updateById(@RequestBody TmsPickupEntity tmsPickup) {
        return R.ok(tmsPickupService.updateById(tmsPickup));
    }

    /**
     * 通过id删除卡派自提信息表
     * @param ids pickupId列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派自提信息表" , description = "通过id删除卡派自提信息表" )
    @SysLog("通过id删除卡派自提信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsPickupService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     *  自提包裹查询
     */
    @Operation(summary = "自提-查询包裹", description = "自提-查询包裹")
    @PostMapping("/search")
    public R search(@RequestParam(value = "pkgNo" , required = false)  String pkgNo, @RequestParam(value = "pickupCode" , required = false)  String pickupCode,
                    @RequestParam(value = "pickupPhone", required = false) String pickupPhone) {

        // 参数至少传一个校验
        if (StrUtil.isAllBlank(pickupPhone, pkgNo, pickupCode)) {
            return LocalizedR.failed("appshelf.at.least.one.parameter", Optional.ofNullable(null));
        }
        return tmsPickupService.listPkginfo(pkgNo,pickupCode, pickupPhone);
    }

    /**
     *  自提包裹提货
     */
    @Operation(summary = "自提包裹提货", description = "自提包裹提货")
    @PostMapping("/pickupPkg")
    public R pickupPkg(@RequestParam("file") @NotBlank(message = "签收图片不能为空") MultipartFile file,
                       @RequestParam("pkgNo") List<String> pkgNo,@RequestParam("pickupCode") List<String> pickupCode) {
        // 包裹号和取件码至少传一个校验
        if (CollUtil.isEmpty(pkgNo) && CollUtil.isEmpty(pickupCode)) {
            return LocalizedR.failed("tms.missing.parameter", Optional.ofNullable(null));
        }
        return tmsPickupService.pickupPkg(file, pkgNo, pickupCode);
    }


    /**
     * 导出excel 表格
     * @param tmsPickup 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsPickup_export')" )
    public List<TmsPickupEntity> export(TmsPickupEntity tmsPickup,Long[] ids) {
        return tmsPickupService.list(Wrappers.lambdaQuery(tmsPickup).in(ArrayUtil.isNotEmpty(ids), TmsPickupEntity::getPickupId, ids));
    }
}