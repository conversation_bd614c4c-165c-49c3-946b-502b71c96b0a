package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsPrintRecordEntity;
import com.jygjexp.jynx.tms.service.TmsPrintRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 云打印记录
 *
 * <AUTHOR>
 * @date 2025-03-19 15:17:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsPrintRecord" )
@Tag(description = "tmsPrintRecord" , name = "云打印记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPrintRecordController {

    private final  TmsPrintRecordService tmsPrintRecordService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsPrintRecord 云打印记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_view')" )
    public R getTmsPrintRecordPage(@ParameterObject Page page, @ParameterObject TmsPrintRecordEntity tmsPrintRecord) {
        LambdaQueryWrapper<TmsPrintRecordEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsPrintRecordService.page(page, wrapper));
    }


    /**
     * 通过id查询云打印记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsPrintRecordService.getById(id));
    }

    /**
     * 新增云打印记录
     * @param tmsPrintRecord 云打印记录
     * @return R
     */
    @Operation(summary = "新增云打印记录" , description = "新增云打印记录" )
    @SysLog("新增云打印记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_add')" )
    public R save(@RequestBody TmsPrintRecordEntity tmsPrintRecord) {
        return R.ok(tmsPrintRecordService.save(tmsPrintRecord));
    }

    /**
     * 修改云打印记录
     * @param tmsPrintRecord 云打印记录
     * @return R
     */
    @Operation(summary = "修改云打印记录" , description = "修改云打印记录" )
    @SysLog("修改云打印记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_edit')" )
    public R updateById(@RequestBody TmsPrintRecordEntity tmsPrintRecord) {
        return R.ok(tmsPrintRecordService.updateById(tmsPrintRecord));
    }

    /**
     * 通过id删除云打印记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除云打印记录" , description = "通过id删除云打印记录" )
    @SysLog("通过id删除云打印记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsPrintRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsPrintRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsPrintRecord_export')" )
    public List<TmsPrintRecordEntity> export(TmsPrintRecordEntity tmsPrintRecord,Long[] ids) {
        return tmsPrintRecordService.list(Wrappers.lambdaQuery(tmsPrintRecord).in(ArrayUtil.isNotEmpty(ids), TmsPrintRecordEntity::getId, ids));
    }
}