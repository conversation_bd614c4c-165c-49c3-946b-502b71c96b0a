package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsReceivableEntity;
import com.jygjexp.jynx.tms.service.TmsReceivableService;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsReceivableExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 财务应收信息表
 *
 * <AUTHOR>
 * @date 2025-07-15 18:18:47
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsReceivable" )
@Tag(description = "tmsReceivable" , name = "财务应收信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsReceivableController {

    private final  TmsReceivableService tmsReceivableService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 财务应收信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_view')" )
    public R getTmsReceivablePage(@ParameterObject Page page, @ParameterObject TmsReceivablePageVo vo) {
        return R.ok(tmsReceivableService.search(page, vo));
    }


    /**
     * 通过id查询财务应收信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @PostMapping("/selectIds" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_view')" )
    public R getById(@RequestParam("id" ) String id) {
        return R.ok(tmsReceivableService.getDetail(id));
    }


    /**
     * 新增财务应收信息表
     * @param tmsReceivable 财务应收信息表
     * @return R
     */
    @Operation(summary = "新增财务应收信息表" , description = "新增财务应收信息表" )
    @SysLog("新增财务应收信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_add')" )
    public R save(@RequestBody TmsReceivableEntity tmsReceivable) {
        return R.ok(tmsReceivableService.save(tmsReceivable));
    }

    /**
     * 修改财务应收信息表
     * @param tmsReceivable 财务应收信息表
     * @return R
     */
    @Operation(summary = "修改财务应收信息表" , description = "修改财务应收信息表" )
    @SysLog("修改财务应收信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_edit')" )
    public R updateById(@RequestBody TmsReceivableEntity tmsReceivable) {
        return R.ok(tmsReceivableService.updateById(tmsReceivable));
    }

    /**
     * 通过id删除财务应收信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除财务应收信息表" , description = "通过id删除财务应收信息表" )
    @SysLog("通过id删除财务应收信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsReceivableService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格 - 仅导出子单数据
     * @param tmsReceivable 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_export')" )
    public List<TmsReceivableExcelVo> export(TmsReceivablePageVo tmsReceivable, Long[] ids) {
        return tmsReceivableService.getExcel(tmsReceivable, ids);
    }

    /**
     * 价格计算API - 基于订单数据计算基础费用价格
     * @param request 价格计算请求参数
     * @return 价格计算结果
     */
    @Operation(summary = "价格计算API", description = "基于订单数据计算基础费用价格(后续需要加上附加费)")
    @PostMapping("/calculate-price")
    @Inner(value = false)
    public PriceCalculationResultVo calculatePrice(@RequestBody PriceCalculationRequestVo request) {
     return  tmsReceivableService.calculatePrice(request);
    }

    /**
     * 价格计算并创建应收记录 - 基于订单数据计算价格并生成应收记录
     * @param request 价格计算请求参数
     * @return 价格计算和应收记录创建结果
     */
    @Operation(summary = "价格计算并创建应收记录", description = "基于订单数据计算价格并生成应收记录")
    @SysLog("价格计算并创建应收记录API")
    @PostMapping("/calculate-price-and-create")
    @Inner(value = false)
//    @PreAuthorize("@pms.hasPermission('tms_tmsReceivable_calculate_create')")
    public PriceCalculationAndReceivableResultVo calculatePriceAndCreateReceivable(@RequestBody PriceCalculationYingShouRequestVo request) {
        return tmsReceivableService.calculatePriceAndCreateReceivable(request);
    }
}
