package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsRegionEntity;
import com.jygjexp.jynx.tms.entity.TmsZipZoneEntity;
import com.jygjexp.jynx.tms.service.TmsRegionService;
import com.jygjexp.jynx.tms.service.TmsZipZoneService;
import com.jygjexp.jynx.tms.vo.TmsRegionPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @date 2025-03-10 17:25:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsRegion" )
@Tag(description = "tmsRegion" , name = "区域信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsRegionController {

    private final  TmsRegionService tmsRegionService;
    private final TmsZipZoneService tmsZipZoneService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 区域信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_view')" )
    public R getTmsRegionPage(@ParameterObject Page page, @ParameterObject TmsRegionPageVo vo) {
        return R.ok(tmsRegionService.search(page, vo));
    }


    /**
     * 通过id查询区域信息表
     * @param regionId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{regionId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_view')" )
    public R getById(@PathVariable("regionId" ) Long regionId) {
        return R.ok(tmsRegionService.getById(regionId));
    }

    /**
     * 新增区域信息表
     * @param tmsRegion 区域信息表
     * @return R
     */
    @Operation(summary = "新增区域信息表" , description = "新增区域信息表" )
    @SysLog("新增区域信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_add')" )
    public R save(@RequestBody TmsRegionEntity tmsRegion) {
        return R.ok(tmsRegionService.save(tmsRegion));
    }

    /**
     * 修改区域信息表
     * @param tmsRegion 区域信息表
     * @return R
     */
    @Operation(summary = "修改区域信息表" , description = "修改区域信息表" )
    @SysLog("修改区域信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_edit')" )
    public R updateById(@RequestBody TmsRegionEntity tmsRegion) {
        return R.ok(tmsRegionService.updateById(tmsRegion));
    }

    /**
     * 通过id删除区域信息表
     * @param ids regionId列表
     * @return R
     */
    @Operation(summary = "通过id删除区域信息表" , description = "通过id删除区域信息表" )
    @SysLog("通过id删除区域信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsRegionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 查询全部承运商划分区域
     *
     * @return R
     */
    @Operation(summary = "查询全部承运商划分区域" , description = "查询全部承运商划分区域" )
    @GetMapping("/listAreaGeometry" )
    public R listAreaGeometry() {
        List<TmsRegionEntity> list = tmsRegionService.lambdaQuery()
                .select(TmsRegionEntity::getRegionId,TmsRegionEntity::getAreaGeometry, TmsRegionEntity::getRegionName, TmsRegionEntity::getIsValid,TmsRegionEntity::getCarrierType)
                .list()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        return R.ok(list);
    }

    // 根据区域id查询路线编号
    @Operation(summary = "根据区域id查询路线编号" , description = "根据区域id查询路线编号" )
    @GetMapping("/listAreaGeometry/{regionId}" )
    public R listAreaGeometry(@PathVariable("regionId" ) Long regionId) {
        LambdaQueryWrapper<TmsZipZoneEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsZipZoneEntity::getRegionId, regionId);
        List<TmsZipZoneEntity> list = tmsZipZoneService.list(wrapper);
        return R.ok(list);
    }


    // 根据城市和邮编获取路线编号
    @Inner(value = false)
    @Operation(summary = "根据区域id查询路线编号" , description = "根据区域id查询路线编号" )
    @GetMapping("/getRouteNo" )
    public String getRouteNo(String city, String zipCode) {
        return tmsRegionService.getRouteNo(city, zipCode);
    }


    /**
     * 查询各个承运商名称和划分的区域
     *
     * @return R
     */
    @Operation(summary = "查询各个承运商名称和划分的区域" , description = "查询各个承运商名称和划分的区域" )
    @GetMapping("/listCarrierAreaGeometry" )
    public R listCarrierAreaGeometry() {
        return R.ok(tmsRegionService.listCarrierAreaGeometry());
    }


    /**
     * 导出excel 表格
     * @param tmsRegion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsRegion_export')" )
    public List<TmsRegionEntity> export(TmsRegionEntity tmsRegion,Long[] ids) {
        return tmsRegionService.list(Wrappers.lambdaQuery(tmsRegion).in(ArrayUtil.isNotEmpty(ids), TmsRegionEntity::getRegionId, ids));
    }
}