package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsReportAndOrderEntity;
import com.jygjexp.jynx.tms.service.TmsReportAndOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 报告-订单中间表
 *
 * <AUTHOR>
 * @date 2025-06-24 10:09:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsReportAndOrder" )
@Tag(description = "tmsReportAndOrder" , name = "报告-订单中间表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsReportAndOrderController {

    private final  TmsReportAndOrderService tmsReportAndOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsReportAndOrder 报告-订单中间表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_view')" )
    public R getTmsReportAndOrderPage(@ParameterObject Page page, @ParameterObject TmsReportAndOrderEntity tmsReportAndOrder) {
        LambdaQueryWrapper<TmsReportAndOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsReportAndOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询报告-订单中间表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsReportAndOrderService.getById(id));
    }

    /**
     * 新增报告-订单中间表
     * @param tmsReportAndOrder 报告-订单中间表
     * @return R
     */
    @Operation(summary = "新增报告-订单中间表" , description = "新增报告-订单中间表" )
    @SysLog("新增报告-订单中间表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_add')" )
    public R save(@RequestBody TmsReportAndOrderEntity tmsReportAndOrder) {
        return R.ok(tmsReportAndOrderService.save(tmsReportAndOrder));
    }

    /**
     * 修改报告-订单中间表
     * @param tmsReportAndOrder 报告-订单中间表
     * @return R
     */
    @Operation(summary = "修改报告-订单中间表" , description = "修改报告-订单中间表" )
    @SysLog("修改报告-订单中间表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_edit')" )
    public R updateById(@RequestBody TmsReportAndOrderEntity tmsReportAndOrder) {
        return R.ok(tmsReportAndOrderService.updateById(tmsReportAndOrder));
    }

    /**
     * 通过id删除报告-订单中间表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除报告-订单中间表" , description = "通过id删除报告-订单中间表" )
    @SysLog("通过id删除报告-订单中间表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsReportAndOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsReportAndOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsReportAndOrder_export')" )
    public List<TmsReportAndOrderEntity> export(TmsReportAndOrderEntity tmsReportAndOrder,Long[] ids) {
        return tmsReportAndOrderService.list(Wrappers.lambdaQuery(tmsReportAndOrder).in(ArrayUtil.isNotEmpty(ids), TmsReportAndOrderEntity::getId, ids));
    }
}