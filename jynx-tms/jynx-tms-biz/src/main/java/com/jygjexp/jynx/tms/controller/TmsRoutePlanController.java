package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsRoutePlanEntity;
import com.jygjexp.jynx.tms.service.TmsRoutePlanService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 路线规划主表
 *
 * <AUTHOR>
 * @date 2025-03-17 15:31:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsRoutePlan" )
@Tag(description = "tmsRoutePlan" , name = "路线规划主表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsRoutePlanController {

    private final  TmsRoutePlanService tmsRoutePlanService;

    /**
     * APP运输单初次路线规划（卡派）
     * @param tmsRoutePlanDto APP运输单初次路线规划（卡派）
     * @return R
     */
    @Operation(summary = "APP运输单初次路线规划（卡派）" , description = "APP运输单初次路线规划（卡派）" )
    @SysLog("APP运输单初次路线规划（卡派）" )
    @PostMapping("/saveRoutePlan")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R saveRoutePlan(@RequestBody TmsRoutePlanDto tmsRoutePlanDto) {
        return tmsRoutePlanService.saveRoutePlan(tmsRoutePlanDto);
    }

    /**
     * APP运输单中途重新规划(跳过)
     * @param tmsRouteReplanningDto APP运输单中途重新规划(跳过)
     * @return R
     */
    @Operation(summary = "APP运输单中途重新规划(跳过)" , description = "APP运输单中途重新规划(跳过)" )
    @SysLog("APP运输单中途重新规划(跳过)" )
    @PostMapping("/routeReplanningToSkip")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R routeReplanningToSkip(@RequestBody TmsRouteReplanningDto tmsRouteReplanningDto) {
        //跳过重新规划
        return tmsRoutePlanService.routeReplanning(tmsRouteReplanningDto,1);
    }

    /**
     * APP运输单中途重新规划(二次派送)
     * @param tmsRouteReplanningDto APP运输单中途重新规划(二次派送)
     * @return R
     */
    @Operation(summary = "APP运输单中途重新规划(二次派送)" , description = "APP运输单中途重新规划(二次派送)" )
    @SysLog("APP运输单中途重新规划(二次派送)" )
    @PostMapping("/routeReplanningToSecondDelivery")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R routeReplanningToSecondDelivery(@RequestBody TmsRouteReplanningDto tmsRouteReplanningDto) {
        //二次派送重新规划
        return tmsRoutePlanService.routeReplanning(tmsRouteReplanningDto,2);
    }


    /**
     * 根据地址查询经纬度
     * @param
     * @return
     */
    @Operation(summary = "根据地址查询经纬度" , description = "根据地址查询经纬度" )
    @GetMapping("/getLatLngByAddress" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getLatLngByAddress(@RequestParam String address) {
        return R.ok(tmsRoutePlanService.getLatLngByAddress(address));
    }

    /**
     * 根据运输单号查询路线分段串
     * @param
     * @return
     */
    @Operation(summary = "根据运输单号查询路线分段串" , description = "根据运输单号查询路线分段串" )
    @GetMapping("/getRoutePlanByShipmentId" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getRoutePlanByShipmentId(@RequestParam String shipmentNo) {
        return tmsRoutePlanService.getRoutePlanByShipmentNo(shipmentNo);
    }

    /**
     * 上传车辆位置更新（只要开始运输到结束运输的司机经纬度-用于表示实时路线，司机空闲的经纬度或者不是同一个运输单的不通过此接口上传！）
     * @param vehicleLocationDto 上传车辆位置更新（只要开始运输到结束运输的司机经纬度-用于表示实时路线，司机空闲的经纬度或者不是同一个运输单的不通过此接口上传！）
     * @return R
     */
    @Operation(summary = "上传车辆位置更新（只要开始运输到结束运输的司机经纬度-用于表示实时路线，司机空闲的经纬度或者不是同一个运输单的不通过此接口上传！）" , description = "上传车辆位置更新（只要开始运输到结束运输的司机经纬度-用于表示实时路线，司机空闲的经纬度或者不是同一个运输单的不通过此接口上传！）" )
    @SysLog("上传车辆位置更新" )
    @PostMapping("/uploadVehicleLocation")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R uploadVehicleLocation(@RequestBody TmsVehicleLocationDto vehicleLocationDto) {
        return tmsRoutePlanService.uploadVehicleLocation(vehicleLocationDto);
    }

    /**
     * 根据运输单号查询最新的车辆位置经纬度
     * @param
     * @return
     */
    @Operation(summary = "根据运输单号查询最新的车辆位置经纬度" , description = "根据运输单号查询最新的车辆位置经纬度" )
    @PostMapping("/getLatestLocaltion" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getLatestLocaltion(@RequestBody List<String> shipmentNos) {
        return tmsRoutePlanService.getLatestLocaltion(shipmentNos);
    }

    /**
     * 根据运输单号查询路线点
     * @param
     * @return
     */
    @Operation(summary = "根据运输单号查询路线点" , description = "根据运输单号查询路线点" )
    @GetMapping("/getRoutePointByShipmentId" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getRoutePointByShipmentNo(@RequestParam String shipmentNo) {
        return R.ok(tmsRoutePlanService.getRoutePointByShipmentNo(shipmentNo));
    }

    /**
     * 跳过委托单
     * @param skipOrderDto 跳过委托单
     * @return R
     */
    @Operation(summary = "跳过委托单" , description = "跳过委托单" )
    @SysLog("跳过委托单" )
    @PostMapping("/skipOrder")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R skipOrder(@RequestBody SkipOrderDto skipOrderDto) {
        return tmsRoutePlanService.skipOrder(skipOrderDto);
    }

    /**
     * 保存当前取货点（送货点）经纬度
     * @param saveVisitStatusDto 保存当前取货点（送货点）经纬度
     * @return R
     */
    @Operation(summary = "保存当前取货点（送货点）经纬度" , description = "保存当前取货点（送货点）经纬度" )
    @SysLog("保存当前取货点（送货点）经纬度" )
    @PostMapping("/saveVisitStatus")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R saveVisitStatus(@RequestBody SaveVisitStatusDto saveVisitStatusDto) {
        //跳过重新规划
        return tmsRoutePlanService.saveVisitStatus(saveVisitStatusDto);
    }

    /**
     * 司机停止接单，将未派完的单放回调度池（运输单解绑未完成的委托单，待重新指派调度）
     * @param driverId 司机停止接单，将未派完的单放回调度池
     * @return R
     */
    @Operation(summary = "司机停止接单，将未派完的单放回调度池)" , description = "司机停止接单，将未派完的单放回调度池" )
    @SysLog("司机停止接单，将未派完的单放回调度池" )
    @GetMapping("/vehicleStopReceiveOrder")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R vehicleStopReceiveOrder(@RequestParam Long driverId) {
        return tmsRoutePlanService.vehicleStopReceiveOrder(driverId);
    }

    /**
     * 根据运输单号查询实际路线轨迹
     * @param shipmentNo 根据运输单号查询实际路线轨迹
     * @return R
     */
    @Operation(summary = "根据运输单号查询实际路线轨迹" , description = "根据运输单号查询实际路线轨迹" )
    @SysLog("根据运输单号查询实际路线轨迹" )
    @GetMapping("/getActualRouteByShipmentNo")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R getActualRouteByShipmentNo(@RequestParam String shipmentNo) {
        return tmsRoutePlanService.getActualRouteByShipmentNo(shipmentNo);
    }


    /**
     * 记录到达取货或者送货点的时间
     * @param entrustedOrderNumber 委托单号（主单）
     * @param isPickup 是否取货点（1：取货点，0：送货点）
     * @param actualArrivalTime 到达时间
     */
    @Operation(summary = "记录到达取货或者送货点的时间" , description = "记录到达取货或者送货点的时间" )
    @SysLog("记录到达取货或者送货点的时间" )
    @PostMapping("/saveActualArrivalTime")
//    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R saveActualArrivalTime(@RequestParam String entrustedOrderNumber, @RequestParam Integer isPickup, @RequestParam LocalDateTime actualArrivalTime) {
        return tmsRoutePlanService.saveActualArrivalTime(entrustedOrderNumber, isPickup, actualArrivalTime);
    }


    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsRoutePlan 路线规划主表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getTmsRoutePlanPage(@ParameterObject Page page, @ParameterObject TmsRoutePlanEntity tmsRoutePlan) {
        LambdaQueryWrapper<TmsRoutePlanEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsRoutePlanService.page(page, wrapper));
    }


    /**
     * 通过id查询路线规划主表
     * @param routePlanId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{routePlanId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_view')" )
    public R getById(@PathVariable("routePlanId" ) Long routePlanId) {
        return R.ok(tmsRoutePlanService.getById(routePlanId));
    }

    /**
     * 新增路线规划主表
     * @param tmsRoutePlan 路线规划主表
     * @return R
     */
    @Operation(summary = "新增路线规划主表" , description = "新增路线规划主表" )
    @SysLog("新增路线规划主表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_add')" )
    public R save(@RequestBody TmsRoutePlanEntity tmsRoutePlan) {
        return R.ok(tmsRoutePlanService.save(tmsRoutePlan));
    }

    /**
     * 修改路线规划主表
     * @param tmsRoutePlan 路线规划主表
     * @return R
     */
    @Operation(summary = "修改路线规划主表" , description = "修改路线规划主表" )
    @SysLog("修改路线规划主表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_edit')" )
    public R updateById(@RequestBody TmsRoutePlanEntity tmsRoutePlan) {
        return R.ok(tmsRoutePlanService.updateById(tmsRoutePlan));
    }

    /**
     * 通过id删除路线规划主表
     * @param ids routePlanId列表
     * @return R
     */
    @Operation(summary = "通过id删除路线规划主表" , description = "通过id删除路线规划主表" )
    @SysLog("通过id删除路线规划主表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsRoutePlanService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsRoutePlan 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsRoutePlan_export')" )
    public List<TmsRoutePlanEntity> export(TmsRoutePlanEntity tmsRoutePlan,Long[] ids) {
        return tmsRoutePlanService.list(Wrappers.lambdaQuery(tmsRoutePlan).in(ArrayUtil.isNotEmpty(ids), TmsRoutePlanEntity::getRoutePlanId, ids));
    }
}