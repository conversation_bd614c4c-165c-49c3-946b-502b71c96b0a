package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsServiceQuoteDto;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceQuoteService;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 服务商报价表
 *
 * <AUTHOR>
 * @date 2025-07-09 17:49:14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsServiceQuote" )
@Tag(description = "tmsServiceQuote" , name = "服务商报价表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsServiceQuoteController {

    private final  TmsServiceQuoteService tmsServiceQuoteService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 服务商报价表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_view')" )
    public R getTmsServiceQuotePage(@ParameterObject Page page, @ParameterObject TmsServiceQuotePageVo vo) {
        Page<TmsServiceQuoteDto> search = tmsServiceQuoteService.search(page, vo);
        return R.ok(search);
    }



    /**
     * 根据id查询详情明细
     * @param id id
     * @return R
     */
    @Operation(summary = "根据id查询详情明细" , description = "根据id查询详情明细" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsServiceQuoteService.getById(id));
    }

    /**
     * 根据报价id查询详情明细
     * @param id id
     * @return R
     */
    @Operation(summary = "根据报价id查询详情明细" , description = "根据报价id查询详情明细" )
    @GetMapping("/getQuotePriceById/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_quotePriceById_view')" )
    public R getQuotePriceById(@PathVariable("id") Long id) {
        return tmsServiceQuoteService.getQuotePriceById(id);
    }

    /**
     * 新增服务商报价表
     * @param tmsServiceQuote 服务商报价表
     * @return R
     */
    @Operation(summary = "新增服务商报价表" , description = "新增服务商报价表" )
    @SysLog("新增服务商报价表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_add')" )
    public R save(@RequestBody TmsServiceQuoteEntity tmsServiceQuote) {
        if (!checkName(tmsServiceQuote)) {
            return LocalizedR.failed("tms.serviceQuote.name.exists", tmsServiceQuote.getQuoteName()+"/"+tmsServiceQuote.getQuoteCode());
        }

        if (!checkChannelName(tmsServiceQuote)) {
            return LocalizedR.failed("tms.serviceQuote.channel.name.exists", Optional.ofNullable(null));
        }
        // 判断可达分区是否已存在报价中
/*        if (Objects.nonNull(tmsServiceQuote.getReachableRegionId())) {
            TmsServiceQuoteEntity reachable = tmsServiceQuoteService.getOne(new LambdaQueryWrapper<TmsServiceQuoteEntity>()
                    .eq(TmsServiceQuoteEntity::getReachableRegionId, tmsServiceQuote.getReachableRegionId()),false);
            if (!Objects.isNull(reachable)) {
                return LocalizedR.failed("tms.serviceQuote.keda.fenqu.exists", Optional.ofNullable(null));
            }
        }*/
        return R.ok(tmsServiceQuoteService.save(tmsServiceQuote));
    }

    /**
     * 修改服务商报价表
     * @param tmsServiceQuote 服务商报价表
     * @return R
     */
    @Operation(summary = "修改服务商报价表" , description = "修改服务商报价表" )
    @SysLog("修改服务商报价表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_edit')" )
    public R updateById(@RequestBody TmsServiceQuoteEntity tmsServiceQuote) {
        if (!checkName(tmsServiceQuote)) {
            return LocalizedR.failed("tms.serviceQuote.name.exists", tmsServiceQuote.getQuoteName()+"/"+tmsServiceQuote.getQuoteCode());
        }
        if (!checkChannelName(tmsServiceQuote)) {
            return LocalizedR.failed("tms.serviceQuote.channel.name.exists", Optional.ofNullable(null));
        }
        // 判断可达分区是否已存在报价中
/*        if (Objects.nonNull(tmsServiceQuote.getReachableRegionId())) {
            TmsServiceQuoteEntity reachable = tmsServiceQuoteService.getOne(new LambdaQueryWrapper<TmsServiceQuoteEntity>()
                    .eq(TmsServiceQuoteEntity::getReachableRegionId, tmsServiceQuote.getReachableRegionId())
                    .ne(TmsServiceQuoteEntity::getId, tmsServiceQuote.getId()),false);
            if (!Objects.isNull(reachable)) {
                return LocalizedR.failed("tms.serviceQuote.keda.fenqu.exists", Optional.ofNullable(null));
            }
        }*/
        return R.ok(tmsServiceQuoteService.updateById(tmsServiceQuote));
    }

    /**
     * 通过id删除服务商报价表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商报价表" , description = "通过id删除服务商报价表" )
    @SysLog("通过id删除服务商报价表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsServiceQuoteService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 判断报价名称与报价代码不可重复
    public Boolean checkName(TmsServiceQuoteEntity tmsServiceQuote) {
        LambdaQueryWrapper<TmsServiceQuoteEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.nested(wrapper ->
                wrapper.eq(TmsServiceQuoteEntity::getQuoteName, tmsServiceQuote.getQuoteName())
                .or()
                .eq(TmsServiceQuoteEntity::getQuoteCode, tmsServiceQuote.getQuoteCode())
        );
        queryWrapper.ne(ObjectUtil.isNotNull(tmsServiceQuote.getId()),TmsServiceQuoteEntity::getId, tmsServiceQuote.getId());
        TmsServiceQuoteEntity byName = tmsServiceQuoteService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    // 判断服务商名称-渠道名称不可重复
    public Boolean checkChannelName(TmsServiceQuoteEntity tmsServiceQuote) {
        LambdaQueryWrapper<TmsServiceQuoteEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TmsServiceQuoteEntity::getChannelId, tmsServiceQuote.getChannelId())
                .eq(TmsServiceQuoteEntity::getProviderId, tmsServiceQuote.getProviderId());
        queryWrapper.ne(ObjectUtil.isNotNull(tmsServiceQuote.getId()),TmsServiceQuoteEntity::getId, tmsServiceQuote.getId());
        TmsServiceQuoteEntity byName = tmsServiceQuoteService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    // 查询所有报价记录
    @Operation(summary = "查询所有报价记录" , description = "查询所有报价记录" )
    @GetMapping("/listQuote" )
    public R listQuote() {
        LambdaQueryWrapper<TmsServiceQuoteEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(TmsServiceQuoteEntity::getCreateTime);
        return R.ok(tmsServiceQuoteService.list(queryWrapper));
    }


    /**
     * 导出excel 表格
     * @param tmsServiceQuote 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuote_export')" )
    public List<TmsServiceQuoteEntity> export(TmsServiceQuoteEntity tmsServiceQuote,Long[] ids) {
        return tmsServiceQuoteService.list(Wrappers.lambdaQuery(tmsServiceQuote).in(ArrayUtil.isNotEmpty(ids), TmsServiceQuoteEntity::getId, ids));
    }*/
}