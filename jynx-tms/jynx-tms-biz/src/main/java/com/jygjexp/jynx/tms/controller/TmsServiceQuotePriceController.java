package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsServiceQuotePriceEntity;
import com.jygjexp.jynx.tms.service.TmsServiceQuotePriceService;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePricePageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceQuotePriceExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

/**
 * 服务商报价-价格配置子表
 *
 * <AUTHOR>
 * @date 2025-07-09 18:33:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsServiceQuotePrice" )
@Tag(description = "tmsServiceQuotePrice" , name = "服务商报价-价格配置子表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsServiceQuotePriceController {

    private final  TmsServiceQuotePriceService tmsServiceQuotePriceService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 服务商报价-价格配置子表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_view')" )
    public R getTmsServiceQuotePricePage(@ParameterObject Page page, @ParameterObject TmsServiceQuotePricePageVo vo) {
        return R.ok(tmsServiceQuotePriceService.search(page, vo));
    }


    /**
     * 通过id查询服务商报价-价格配置子表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsServiceQuotePriceService.getById(id));
    }

    /**
     * 新增服务商报价-价格配置子表
     * @param tmsServiceQuotePrice 服务商报价-价格配置子表
     * @return R
     */
    @Operation(summary = "新增服务商报价-价格配置子表" , description = "新增服务商报价-价格配置子表" )
    @SysLog("新增服务商报价-价格配置子表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_add')" )
    public R save(@RequestBody TmsServiceQuotePriceEntity tmsServiceQuotePrice) {
        return tmsServiceQuotePriceService.saveServiceQuotePrice(tmsServiceQuotePrice);
    }

    /**
     * 修改服务商报价-价格配置子表
     * @param tmsServiceQuotePrice 服务商报价-价格配置子表
     * @return R
     */
    @Operation(summary = "修改服务商报价-价格配置子表" , description = "修改服务商报价-价格配置子表" )
    @SysLog("修改服务商报价-价格配置子表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_edit')" )
    public R updateById(@RequestBody TmsServiceQuotePriceEntity tmsServiceQuotePrice) {
        return tmsServiceQuotePriceService.updateServiceQuotePriceById(tmsServiceQuotePrice);
    }

    /**
     * 通过id删除服务商报价-价格配置子表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商报价-价格配置子表" , description = "通过id删除服务商报价-价格配置子表" )
    @SysLog("通过id删除服务商报价-价格配置子表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsServiceQuotePriceService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsServiceQuotePrice 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_export')" )
    public List<TmsServiceQuotePriceExcelVo> export(TmsServiceQuotePricePageVo tmsServiceQuotePrice, Long[] ids) {
        return tmsServiceQuotePriceService.getServiceQuotePriceExport(tmsServiceQuotePrice,ids);
    }

    /**
     * 导入价格配置
     * @param file
     * @return
     */
    @Operation(summary = "导入价格配置" , description = "导入价格配置" )
    @SysLog("导入价格配置" )
    @PostMapping("/_import")
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceQuotePrice_import')")
    public R importQuote(@RequestParam("file") MultipartFile file, @RequestParam("quoteId") Long quoteId,@RequestParam("regionName") String regionName) {
        return tmsServiceQuotePriceService.processFile(file,quoteId,regionName);
    }
}