package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceRegionService;
import com.jygjexp.jynx.tms.vo.TmsServiceReginPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 服务商邮编分区配置表
 *
 * <AUTHOR>
 * @date 2025-07-10 10:34:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsServiceRegion" )
@Tag(description = "tmsServiceRegion" , name = "服务商邮编分区配置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsServiceRegionController {

    private final  TmsServiceRegionService tmsServiceRegionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 服务商邮编分区配置表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_view')" )
    public R getTmsServiceRegionPage(@ParameterObject Page page, @ParameterObject TmsServiceReginPageVo vo) {
        return R.ok(tmsServiceRegionService.search(page, vo));
    }


    /**
     * 通过id查询服务商邮编分区配置表
     * @param regionId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{regionId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_view')" )
    public R getById(@PathVariable("regionId" ) Long regionId) {
        return R.ok(tmsServiceRegionService.getById(regionId));
    }

    /**
     * 新增服务商邮编分区配置表
     * @param tmsServiceRegion 服务商邮编分区配置表
     * @return R
     */
    @Operation(summary = "新增服务商邮编分区配置表" , description = "新增服务商邮编分区配置表" )
    @SysLog("新增服务商邮编分区配置表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_add')" )
    public R save(@RequestBody TmsServiceRegionEntity tmsServiceRegion) {
        if (!checkName(tmsServiceRegion)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.exists", tmsServiceRegion.getRegionName()+"/"+tmsServiceRegion.getRegionCode());
        }
        return R.ok(tmsServiceRegionService.save(tmsServiceRegion));
    }

    /**
     * 修改服务商邮编分区配置表
     * @param tmsServiceRegion 服务商邮编分区配置表
     * @return R
     */
    @Operation(summary = "修改服务商邮编分区配置表" , description = "修改服务商邮编分区配置表" )
    @SysLog("修改服务商邮编分区配置表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_edit')" )
    public R updateById(@RequestBody TmsServiceRegionEntity tmsServiceRegion) {
        if (!checkName(tmsServiceRegion)) {
            return LocalizedR.failed("tms.serviceProvider.postalCode.exists", tmsServiceRegion.getRegionName()+"/"+tmsServiceRegion.getRegionCode());
        }
        return R.ok(tmsServiceRegionService.updateById(tmsServiceRegion));
    }

    // 判断服务商邮编分区名称与代码不可重复
    public Boolean checkName(TmsServiceRegionEntity tmsServiceRegion) {
        LambdaQueryWrapper<TmsServiceRegionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.nested(wrapper ->
                wrapper.eq(TmsServiceRegionEntity::getRegionCode, tmsServiceRegion.getRegionCode())
                        .or()
                        .eq(TmsServiceRegionEntity::getRegionName, tmsServiceRegion.getRegionName())
        );
        if (ObjectUtil.isNotNull(tmsServiceRegion.getRegionType())){
            queryWrapper.eq(TmsServiceRegionEntity::getRegionType, tmsServiceRegion.getRegionType());
        }
        queryWrapper.ne(ObjectUtil.isNotNull(tmsServiceRegion.getRegionId()),TmsServiceRegionEntity::getRegionId, tmsServiceRegion.getRegionId());
        TmsServiceRegionEntity byName = tmsServiceRegionService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 通过id删除服务商邮编分区配置表
     * @param ids regionId列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商邮编分区配置表" , description = "通过id删除服务商邮编分区配置表" )
    @SysLog("通过id删除服务商邮编分区配置表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsServiceRegionService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 查询全部可达分区
    @Operation(summary = "查询全部可达分区" , description = "查询全部可达分区" )
    @GetMapping("/reachableRegion/list")
    //@PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_view')" )
    public R reachableRegion() {
        LambdaQueryWrapper<TmsServiceRegionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TmsServiceRegionEntity::getRegionId, TmsServiceRegionEntity::getRegionName, TmsServiceRegionEntity::getIsValid).groupBy(TmsServiceRegionEntity::getRegionId);
        queryWrapper.eq(TmsServiceRegionEntity::getRegionType, true);
        queryWrapper.orderByDesc(TmsServiceRegionEntity::getCreateTime);
        return R.ok(tmsServiceRegionService.list(queryWrapper));
    }

    // 查询全部不可达分区
    @Operation(summary = "查询全部不可达分区" , description = "查询全部不可达分区" )
    @GetMapping("/unreachableRegion/list")
    //@PreAuthorize("@pms.hasPermission('tms_tmsServiceProvider_view')" )
    public R unreachableRegion() {
        LambdaQueryWrapper<TmsServiceRegionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TmsServiceRegionEntity::getRegionId, TmsServiceRegionEntity::getRegionName, TmsServiceRegionEntity::getIsValid).groupBy(TmsServiceRegionEntity::getRegionId);
        queryWrapper.eq(TmsServiceRegionEntity::getRegionType, false);
        queryWrapper.orderByDesc(TmsServiceRegionEntity::getCreateTime);
        return R.ok(tmsServiceRegionService.list(queryWrapper));
    }


    /**
     * 导出excel 表格
     * @param tmsServiceRegion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsServiceRegion_export')" )
    public List<TmsServiceRegionEntity> export(TmsServiceRegionEntity tmsServiceRegion,Long[] ids) {
        return tmsServiceRegionService.list(Wrappers.lambdaQuery(tmsServiceRegion).in(ArrayUtil.isNotEmpty(ids), TmsServiceRegionEntity::getRegionId, ids));
    }
}