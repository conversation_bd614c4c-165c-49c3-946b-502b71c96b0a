package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsPostalCodeDTO;
import com.jygjexp.jynx.tms.dto.TmsSiteDTO;
import com.jygjexp.jynx.tms.entity.TmsOverAreaEntity;
import com.jygjexp.jynx.tms.entity.TmsRegionEntity;
import com.jygjexp.jynx.tms.entity.TmsSiteEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsRoutePlanService;
import com.jygjexp.jynx.tms.service.TmsSiteService;
import com.jygjexp.jynx.tms.vo.TmsSitePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 卡派站点
 *
 * <AUTHOR>
 * @date 2025-03-11 14:37:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSite")
@Tag(description = "tmsSite", name = "卡派站点管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSiteController {

    private final TmsSiteService tmsSiteService;
    private final TmsRoutePlanService routePlanService;

    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param tmsSite 卡派站点
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_view')")
    public R getTmsSitePage(@ParameterObject Page page, @ParameterObject TmsSitePageVo tmsSite) {
        return R.ok(tmsSiteService.search(page, tmsSite));
    }


    /**
     * 通过id查询卡派站点
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_view')")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsSiteService.getById(id));
    }

    /**
     * 新增卡派站点
     *
     * @param tmsSite 卡派站点
     * @return R
     */
    @Operation(summary = "新增卡派站点", description = "新增卡派站点")
    @SysLog("新增卡派站点")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_add')")
    public R save(@RequestBody TmsSiteEntity tmsSite) {
        return R.ok(tmsSiteService.saveSite(tmsSite));
    }

    /**
     * 修改卡派站点
     *
     * @param tmsSite 卡派站点
     * @return R
     */
    @Operation(summary = "修改卡派站点", description = "修改卡派站点")
    @SysLog("修改卡派站点")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_edit')")
    public R updateById(@RequestBody TmsSiteEntity tmsSite) {
        // 校验站点地址不为null
        if (tmsSite.getSiteLat() == null && StrUtil.isNotBlank(tmsSite.getSiteAddress()) ) {
            // 根据地址查询经纬度
            String destLatLng = routePlanService.getLatLngByAddress(tmsSite.getSiteAddress());
            if (StrUtil.isNotBlank(destLatLng)) {
                String[] split = destLatLng.split(",");
                tmsSite.setSiteLat(new BigDecimal(split[0]));
                tmsSite.setSiteLng(new BigDecimal(split[1]));
            }
        }
        return R.ok(tmsSiteService.updateById(tmsSite));
    }

    /**
     * 通过id删除卡派站点
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派站点", description = "通过id删除卡派站点")
    @SysLog("通过id删除卡派站点")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_del')")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsSiteService.removeBatchByIds(CollUtil.toList(ids)));
    }


    // 获取全部站点信息以及经纬度
    @GetMapping("/getAllSite")
    public R getAllSite() {
        return R.ok(tmsSiteService.list().stream()
                .map(entity -> new TmsSiteDTO(entity.getId(), entity.getSiteName(), entity.getSiteType(), entity.getSiteLat()
                        , entity.getSiteLng(), entity.getParentWarehouseId()))
                .distinct().collect(Collectors.toList()));
    }


    /**
     * 获取所有分拣中心
     */
    @Operation(summary = "获取所有分拣中心" , description = "获取所有分拣中心" )
    @GetMapping("/getAllSortWarehouse" )
    @Inner(value = false)
    public R getAllSortWarehouse() {
        LambdaQueryWrapper<TmsSiteEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsSiteEntity::getIsValid, true)
                //匹配对应的一级仓
                .eq(TmsSiteEntity::getSiteType, 1);
        return R.ok(tmsSiteService.list(wrapper));
    }


    // 根据仓库id获取名称
    @GetMapping("/getWarehouseName")
    @Inner(value = false)
    public String getWarehouseName(@RequestParam Long warehouseId) {
        return tmsSiteService.getById(warehouseId).getSiteName();
    }

    /**
     * 导出excel 表格
     *
     * @param tmsSite 查询条件
     * @param ids     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSite_export')")
    public List<TmsSiteEntity> export(TmsSiteEntity tmsSite, Long[] ids) {
        return tmsSiteService.list(Wrappers.lambdaQuery(tmsSite).in(ArrayUtil.isNotEmpty(ids), TmsSiteEntity::getId, ids));
    }
}