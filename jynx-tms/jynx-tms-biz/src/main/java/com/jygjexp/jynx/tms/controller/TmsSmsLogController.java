package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsSmsLogEntity;
import com.jygjexp.jynx.tms.service.TmsSmsLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 中大件短信发送日志表
 *
 * <AUTHOR>
 * @date 2025-04-23 18:23:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSmsLog" )
@Tag(description = "tmsSmsLog" , name = "中大件短信发送日志表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSmsLogController {

    private final  TmsSmsLogService tmsSmsLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsSmsLog 中大件短信发送日志表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_view')" )
    public R getTmsSmsLogPage(@ParameterObject Page page, @ParameterObject TmsSmsLogEntity tmsSmsLog) {
        LambdaQueryWrapper<TmsSmsLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsSmsLogService.page(page, wrapper));
    }


    /**
     * 通过id查询中大件短信发送日志表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsSmsLogService.getById(id));
    }

    /**
     * 新增中大件短信发送日志表
     * @param tmsSmsLog 中大件短信发送日志表
     * @return R
     */
    @Operation(summary = "新增中大件短信发送日志表" , description = "新增中大件短信发送日志表" )
    @SysLog("新增中大件短信发送日志表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_add')" )
    public R save(@RequestBody TmsSmsLogEntity tmsSmsLog) {
        return R.ok(tmsSmsLogService.save(tmsSmsLog));
    }

    /**
     * 修改中大件短信发送日志表
     * @param tmsSmsLog 中大件短信发送日志表
     * @return R
     */
    @Operation(summary = "修改中大件短信发送日志表" , description = "修改中大件短信发送日志表" )
    @SysLog("修改中大件短信发送日志表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_edit')" )
    public R updateById(@RequestBody TmsSmsLogEntity tmsSmsLog) {
        return R.ok(tmsSmsLogService.updateById(tmsSmsLog));
    }

    /**
     * 通过id删除中大件短信发送日志表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件短信发送日志表" , description = "通过id删除中大件短信发送日志表" )
    @SysLog("通过id删除中大件短信发送日志表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsSmsLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsSmsLog 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSmsLog_export')" )
    public List<TmsSmsLogEntity> export(TmsSmsLogEntity tmsSmsLog,Long[] ids) {
        return tmsSmsLogService.list(Wrappers.lambdaQuery(tmsSmsLog).in(ArrayUtil.isNotEmpty(ids), TmsSmsLogEntity::getId, ids));
    }
}