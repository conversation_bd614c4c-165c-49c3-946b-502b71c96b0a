package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsLabelEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingGridEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsSortingGridService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 格口管理
 *
 * <AUTHOR>
 * @date 2025-04-23 18:42:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSortingGrid" )
@Tag(description = "tmsSortingGrid" , name = "格口管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSortingGridController {

    private final  TmsSortingGridService tmsSortingGridService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param entity 格口管理
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_view')" )
    public R getTmsSortingGridPage(@ParameterObject Page page, @ParameterObject TmsSortingGridEntity entity) {
        LambdaQueryWrapper<TmsSortingGridEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(entity.getIsValid()), TmsSortingGridEntity::getIsValid, entity.getIsValid())        // 格口状态
                .like(StrUtil.isNotBlank(entity.getGridName()), TmsSortingGridEntity::getGridName, entity.getGridName())    // 格口名称
                .like(StrUtil.isNotBlank(entity.getGridCode()), TmsSortingGridEntity::getGridCode, entity.getGridCode())    // 格口编码
                .eq(ObjectUtil.isNotNull(entity.getWarehouseId()), TmsSortingGridEntity::getWarehouseId, entity.getWarehouseId())
                .orderByDesc(TmsSortingGridEntity::getCreateTime);
        return R.ok(tmsSortingGridService.page(page, wrapper));
    }


    /**
     * 通过id查询格口管理
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsSortingGridService.getById(id));
    }

    /**
     * 新增格口管理
     * @param tmsSortingGrid 格口管理
     * @return R
     */
    @Operation(summary = "新增格口管理" , description = "新增格口管理" )
    @SysLog("新增格口管理" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_add')" )
    public R save(@RequestBody TmsSortingGridEntity tmsSortingGrid) {
      return   tmsSortingGridService.saveGrid(tmsSortingGrid);
    }

    /**
     * 修改格口管理
     * @param tmsSortingGrid 格口管理
     * @return R
     */
    @Operation(summary = "修改格口管理" , description = "修改格口管理" )
    @SysLog("修改格口管理" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_edit')" )
    public R updateById(@RequestBody TmsSortingGridEntity tmsSortingGrid) {
      return  tmsSortingGridService.updateGrid(tmsSortingGrid);
    }

    /**
     * 通过id删除格口管理
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除格口管理" , description = "通过id删除格口管理" )
    @SysLog("通过id删除格口管理" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsSortingGridService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 根据格口编码查询格口信息
    @GetMapping("/getByGridIdNos")
    @Inner(value = false)
    public List<TmsSortingGridEntity> getByGridIdNos(@RequestParam Long[] gridIds) {
        String gridIdNos = ArrayUtil.join(gridIds, ",");
        List<String> gridIdList = Arrays.stream(gridIdNos.trim().split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        List<TmsSortingGridEntity> list = tmsSortingGridService.list(new LambdaQueryWrapper<TmsSortingGridEntity>()
                .in(TmsSortingGridEntity::getId, gridIdList)
                .eq(TmsSortingGridEntity::getIsValid, 1));  // 有效格口
        return list;
    }

    // 启用格口列表
    @GetMapping("/listGrids")
    @Operation(summary = "启用格口列表" , description = "启用格口列表" )
    @SysLog("启用格口列表" )
    @Inner(value = false)
    public R<List<TmsSortingGridEntity>> listGrids() {
        List<TmsSortingGridEntity> list = tmsSortingGridService.list(new LambdaQueryWrapper<TmsSortingGridEntity>()
                .eq(TmsSortingGridEntity::getIsValid, 1));  // 有效格口
        return R.ok(list);
    }


    /**
     * 导出excel 表格
     * @param tmsSortingGrid 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingGrid_export')" )
    public List<TmsSortingGridEntity> export(TmsSortingGridEntity tmsSortingGrid,Long[] ids) {
        return tmsSortingGridService.list(Wrappers.lambdaQuery(tmsSortingGrid).in(ArrayUtil.isNotEmpty(ids), TmsSortingGridEntity::getId, ids));
    }
}