package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsSortingRuleEntity;
import com.jygjexp.jynx.tms.service.TmsSortingRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 分拣规则表
 *
 * <AUTHOR>
 * @date 2025-06-12 20:04:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSortingRule" )
@Tag(description = "tmsSortingRule" , name = "分拣规则表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSortingRuleController {

    private final  TmsSortingRuleService tmsSortingRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsSortingRule 分拣规则表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_view')" )
    public R getTmsSortingRulePage(@ParameterObject Page page, @ParameterObject TmsSortingRuleEntity tmsSortingRule) {
        LambdaQueryWrapper<TmsSortingRuleEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsSortingRuleService.page(page, wrapper));
    }


    /**
     * 通过id查询分拣规则表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsSortingRuleService.getById(id));
    }

    /**
     * 新增分拣规则表
     * @param tmsSortingRule 分拣规则表
     * @return R
     */
    @Operation(summary = "新增分拣规则表" , description = "新增分拣规则表" )
    @SysLog("新增分拣规则表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_add')" )
    public R save(@RequestBody TmsSortingRuleEntity tmsSortingRule) {
        return R.ok(tmsSortingRuleService.save(tmsSortingRule));
    }

    /**
     * 修改分拣规则表
     * @param tmsSortingRule 分拣规则表
     * @return R
     */
    @Operation(summary = "修改分拣规则表" , description = "修改分拣规则表" )
    @SysLog("修改分拣规则表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_edit')" )
    public R updateById(@RequestBody TmsSortingRuleEntity tmsSortingRule) {
        return R.ok(tmsSortingRuleService.updateById(tmsSortingRule));
    }

    /**
     * 通过id删除分拣规则表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除分拣规则表" , description = "通过id删除分拣规则表" )
    @SysLog("通过id删除分拣规则表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsSortingRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsSortingRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRule_export')" )
    public List<TmsSortingRuleEntity> export(TmsSortingRuleEntity tmsSortingRule,Long[] ids) {
        return tmsSortingRuleService.list(Wrappers.lambdaQuery(tmsSortingRule).in(ArrayUtil.isNotEmpty(ids), TmsSortingRuleEntity::getId, ids));
    }
}