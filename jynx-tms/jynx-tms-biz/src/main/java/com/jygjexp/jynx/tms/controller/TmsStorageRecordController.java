package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsInventoryManagementEntity;
import com.jygjexp.jynx.tms.entity.TmsStorageRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsStorageandorderEntity;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsInventoryManagementService;
import com.jygjexp.jynx.tms.service.TmsStorageRecordService;
import com.jygjexp.jynx.tms.service.TmsStorageandorderService;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsStorageRecordAddVo;
import com.jygjexp.jynx.tms.vo.TmsStorageRecordPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 中大件入库记录
 *
 * <AUTHOR>
 * @date 2025-04-02 20:50:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorageRecord" )
@Tag(description = "tmsStorageRecord" , name = "卡派入库记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorageRecordController {

    private final  TmsStorageRecordService tmsStorageRecordService;
    private final TmsCustomerOrderService customerOrderService;
    private final TmsStorageandorderService storageandorderService;
    private final TmsInventoryManagementService inventoryManagementService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 入库记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_view')" )
    public R getTmsStorageRecordPage(@ParameterObject Page page, @ParameterObject TmsStorageRecordPageVo vo) {
        return R.ok(tmsStorageRecordService.search(page, vo));
    }

    /**
     * 跟踪单号分页查询
     * @param page 分页对象
     * @param orderPageVo 客户订单
     * @return
     */
    @Operation(summary = "跟踪单号分页查询" , description = "跟踪单号分页查询" )
    @GetMapping("/customer/page" )
    public R getCustomerOrderPage(@ParameterObject Page page, @ParameterObject TmsCustomerOrderPageVo orderPageVo) {
        return R.ok(tmsStorageRecordService.getCustomerOrderPage(page, orderPageVo));
    }

    /**
     * 通过id查询卡派入库记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return tmsStorageRecordService.selectById(id);
    }

    /**
     * 新增卡派入库记录
     * @param tmsStorageRecord 卡派入库记录
     * @return R
     */
    @Operation(summary = "新增入库记录" , description = "新增入库记录" )
    @SysLog("新增入库记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_add')" )
    public R save(@RequestBody TmsStorageRecordAddVo tmsStorageRecord) {
        return R.ok(tmsStorageRecordService.saveStorageRecord(tmsStorageRecord));
    }

    /**
     * 修改入库记录
     * @param tmsStorageRecord 入库记录
     * @return R
     */
    @Operation(summary = "修改入库记录" , description = "修改入库记录" )
    @SysLog("修改入库记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_edit')" )
    public R updateById(@RequestBody TmsStorageRecordEntity tmsStorageRecord) {
        return R.ok(tmsStorageRecordService.updateById(tmsStorageRecord));
    }

    /**
     * 通过id删除入库记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除入库记录" , description = "通过id删除入库记录" )
    @SysLog("通过id删除入库记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return tmsStorageRecordService.removeStorageById(ids);
    }


    /**
     * 导出excel 表格
     * @param tmsStorageRecord 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageRecord_export')" )
    public List<TmsStorageRecordEntity> export(TmsStorageRecordEntity tmsStorageRecord,Long[] ids) {
        return tmsStorageRecordService.list(Wrappers.lambdaQuery(tmsStorageRecord).in(ArrayUtil.isNotEmpty(ids), TmsStorageRecordEntity::getId, ids));
    }
}