package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.idempotent.annotation.Idempotent;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 门店余额表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:49:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreBalance")
@Tag(description = "tmsStoreBalance", name = "门店余额表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreBalanceController {

    private final TmsStoreBalanceService tmsStoreBalanceService;

    /**
     * 分页查询
     *
     * @param page            分页对象
     * @param tmsStoreBalance 门店余额表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_view')")
    public R getTmsStoreBalancePage(@ParameterObject Page page, @ParameterObject TmsStoreBalanceEntity tmsStoreBalance) {
        LambdaQueryWrapper<TmsStoreBalanceEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStoreBalanceService.page(page, wrapper));
    }

    /**
     * 通过id查询门店余额表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_view')")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsStoreBalanceService.getById(id));
    }

    /**
     * 新增门店余额表
     *
     * @param tmsStoreBalance 门店余额表
     * @return R
     */
    @Operation(summary = "新增门店余额表", description = "新增门店余额表")
    @SysLog("新增门店余额表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_add')")
    public R save(@RequestBody TmsStoreBalanceEntity tmsStoreBalance) {
        return R.ok(tmsStoreBalanceService.save(tmsStoreBalance));
    }

    /**
     * 初始化所有客户的余额
     * @return
     */
    @Operation(summary = "初始化")
    @PostMapping("/init")
    public R<Boolean> init(){
       return R.ok(tmsStoreBalanceService.init());
    }

    /**
     * 查询当前用户的余额
     * @return
     */
    @Operation(summary = "查询当前用户的余额")
    @GetMapping("/current")
    public R<TmsStoreBalanceEntity> getCurrent() {
        return R.ok(tmsStoreBalanceService.getCurrent());
    }

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    @Idempotent(key = "#id", expireTime = 3)
    @Operation(summary = "线下充值")
    @PostMapping("/{id}/offline/recharge")
    public R<Boolean> offlineRecharge(@PathVariable Long id,
                                      @RequestBody @Valid TmsStoreBalanceOfflineRecharge offlineRecharge) {
        return R.ok(tmsStoreBalanceService.offlineRecharge(id, offlineRecharge));
    }

    /**
     * 修改门店余额表
     *
     * @param tmsStoreBalance 门店余额表
     * @return R
     */
    @Operation(summary = "修改门店余额表", description = "修改门店余额表")
    @SysLog("修改门店余额表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_edit')")
    public R updateById(@RequestBody TmsStoreBalanceEntity tmsStoreBalance) {
        return R.ok(tmsStoreBalanceService.updateById(tmsStoreBalance));
    }

    /**
     * 通过id删除门店余额表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店余额表", description = "通过id删除门店余额表")
    @SysLog("通过id删除门店余额表")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_del')")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreBalanceService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 导出excel 表格
     *
     * @param tmsStoreBalance 查询条件
     * @param ids             导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBalance_export')")
    public List<TmsStoreBalanceEntity> export(TmsStoreBalanceEntity tmsStoreBalance, Long[] ids) {
        return tmsStoreBalanceService.list(Wrappers.lambdaQuery(tmsStoreBalance).in(ArrayUtil.isNotEmpty(ids), TmsStoreBalanceEntity::getId, ids));
    }

    /**
     * 查询指定用户的余额
     * @return
     */
    @Operation(summary = "查询指定用户的余额")
    @GetMapping("/specify/{customerId}")
    public R<TmsStoreBalanceEntity> getSpecifyCustomerBalance(@PathVariable("customerId") Long customerId) {
        return R.ok(tmsStoreBalanceService.getByStoreCustomerId(customerId));
    }
}
