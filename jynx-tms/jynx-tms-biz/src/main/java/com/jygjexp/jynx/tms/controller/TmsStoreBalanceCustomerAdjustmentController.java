package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceCustomerAdjustmentQueryDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceCustomerAdjustmentEntity;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceCustomerAdjustmentService;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceCustomerAdjustmentExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户余额调账表
 *
 * <AUTHOR>
 * @date 2025-08-20 13:53:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreBalanceCustomerAdjustment")
@Tag(description = "tmsStoreBalanceCustomerAdjustment", name = "客户余额调账表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreBalanceCustomerAdjustmentController {

    private final TmsStoreBalanceCustomerAdjustmentService tmsStoreBalanceCustomerAdjustmentService;

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_customerAdjustment_view')")
    public R getTmsStoreBalanceCustomerAdjustmentPage(@ParameterObject Page page, @ParameterObject TmsStoreBalanceCustomerAdjustmentQueryDto tmsStoreBalanceCustomerAdjustment) {
        return R.ok(tmsStoreBalanceCustomerAdjustmentService.pageAdjBalance(page, tmsStoreBalanceCustomerAdjustment));
    }

    /**
     * 新增客户余额调账表
     *
     * @param tmsStoreBalanceCustomerAdjustment 客户余额调账表
     * @return R
     */
    @Operation(summary = "新增客户余额调账表", description = "新增客户余额调账表")
    @SysLog("新增客户余额调账表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_customerAdjustment_add')")
    public R save(@RequestBody TmsStoreBalanceCustomerAdjustmentEntity tmsStoreBalanceCustomerAdjustment) {
        return R.ok(tmsStoreBalanceCustomerAdjustmentService.saveAdjBalance(tmsStoreBalanceCustomerAdjustment));
    }

    /**
     * 导出excel 表格
     *
     * @param tmsStoreBalanceCustomerAdjustment 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_customerAdjustment_export')")
    public List<TmsStoreBalanceCustomerAdjustmentExcelVo> export(@RequestBody TmsStoreBalanceCustomerAdjustmentQueryDto tmsStoreBalanceCustomerAdjustment) {
        return tmsStoreBalanceCustomerAdjustmentService.export(tmsStoreBalanceCustomerAdjustment);
    }
}