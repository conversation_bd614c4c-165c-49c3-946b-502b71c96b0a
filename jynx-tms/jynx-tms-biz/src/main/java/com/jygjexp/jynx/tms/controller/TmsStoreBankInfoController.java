package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreBankInfoEntity;
import com.jygjexp.jynx.tms.service.TmsStoreBankInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 门店收款银行信息表
 *
 * <AUTHOR>
 * @date 2025-07-09 11:16:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreBankInfo" )
@Tag(description = "tmsStoreBankInfo" , name = "门店收款银行信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreBankInfoController {

    private final  TmsStoreBankInfoService tmsStoreBankInfoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreBankInfo 门店收款银行信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_view')" )
    public R getTmsStoreBankInfoPage(@ParameterObject Page page, @ParameterObject TmsStoreBankInfoEntity tmsStoreBankInfo) {
        LambdaQueryWrapper<TmsStoreBankInfoEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStoreBankInfoService.page(page, wrapper));
    }


    /**
     * 通过id查询门店收款银行信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreBankInfoService.getById(id));
    }

    /**
     * 新增门店收款银行信息表
     * @param tmsStoreBankInfo 门店收款银行信息表
     * @return R
     */
    @Operation(summary = "新增门店收款银行信息表" , description = "新增门店收款银行信息表" )
    @SysLog("新增门店收款银行信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_add')" )
    public R save(@RequestBody TmsStoreBankInfoEntity tmsStoreBankInfo) {
        return R.ok(tmsStoreBankInfoService.save(tmsStoreBankInfo));
    }

    /**
     * 修改门店收款银行信息表
     * @param tmsStoreBankInfo 门店收款银行信息表
     * @return R
     */
    @Operation(summary = "修改门店收款银行信息表" , description = "修改门店收款银行信息表" )
    @SysLog("修改门店收款银行信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_edit')" )
    public R updateById(@RequestBody TmsStoreBankInfoEntity tmsStoreBankInfo) {
        return R.ok(tmsStoreBankInfoService.updateById(tmsStoreBankInfo));
    }

    /**
     * 通过id删除门店收款银行信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店收款银行信息表" , description = "通过id删除门店收款银行信息表" )
    @SysLog("通过id删除门店收款银行信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreBankInfoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStoreBankInfo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreBankInfo_export')" )
    public List<TmsStoreBankInfoEntity> export(TmsStoreBankInfoEntity tmsStoreBankInfo,Long[] ids) {
        return tmsStoreBankInfoService.list(Wrappers.lambdaQuery(tmsStoreBankInfo).in(ArrayUtil.isNotEmpty(ids), TmsStoreBankInfoEntity::getId, ids));
    }
}