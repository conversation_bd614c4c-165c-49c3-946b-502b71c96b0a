package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.common.core.constant.CacheConstants;
import com.jygjexp.jynx.common.core.constant.enums.LoginTypeEnum;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsPhoneEmailDto;
import com.jygjexp.jynx.tms.dto.TmsStoreDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.feign.RemoteTmsAppMobileService;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import com.jygjexp.jynx.tms.service.TmsStoreService;
import com.jygjexp.jynx.tms.utils.AESUtil;
import com.jygjexp.jynx.tms.utils.MailSenderUtil;
import com.jygjexp.jynx.tms.vo.TmsStoreExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStorePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * @author: daiyuxuan
 * @create: 2025/7/8
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStore")
@Tag(description = "tmsStore", name = "门店端管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreController {
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteTmsAppMobileService remoteTmsAppMobileService;
    private final StringRedisTemplate redisTemplate;
    private final TmsStoreCustomerService storeCustomerService;

    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    private final TmsStoreService tmsStoreService;

    /**
     * 分页查询
     *
     * @param page     分页对象
     * @param tmsStore 门店信息表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_view')")
    public R getTmsStorePage(@ParameterObject Page page, @ParameterObject TmsStorePageVo tmsStore) {
        LambdaQueryWrapper<TmsStoreEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like((StrUtil.isNotBlank(tmsStore.getStoreName())), TmsStoreEntity::getStoreName, tmsStore.getStoreName())
                .like((StrUtil.isNotBlank(tmsStore.getStoreCode())), TmsStoreEntity::getStoreCode, tmsStore.getStoreCode())
                .eq((ObjectUtil.isNotNull(tmsStore.getStatus())), TmsStoreEntity::getStatus, tmsStore.getStatus())
                .between((ObjectUtil.isNotNull(tmsStore.getBeginTime()) && ObjectUtil.isNotNull(tmsStore.getEndTime())), TmsStoreEntity::getCreateTime, tmsStore.getBeginTime(), tmsStore.getEndTime())
                .orderByDesc(TmsStoreEntity::getCreateTime);
        return R.ok(tmsStoreService.page(page, wrapper));
    }


    /**
     * 通过id查询门店信息表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_view')")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsStoreService.getStoreDetailById(id));
    }

    /**
     * 新增门店信息表
     *
     * @param tmsStore 门店信息表
     * @return R
     */
    @Operation(summary = "新增门店信息表", description = "新增门店信息表")
    @SysLog("新增门店信息表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_add')")
    public R save(@Valid @RequestBody TmsStoreDTO tmsStore) {
        return tmsStoreService.saveStroe(tmsStore);
    }

    /**
     * 修改门店信息表
     *
     * @param tmsStore 门店信息表
     * @return R
     */
    @Operation(summary = "修改门店信息表", description = "修改门店信息表")
    @SysLog("修改门店信息表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_edit')")
    public R updateById(@Valid @RequestBody TmsStoreDTO tmsStore) {
        return R.ok(tmsStoreService.updateStoreById(tmsStore));
    }

    /**
     * 通过id删除门店信息表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店信息表", description = "通过id删除门店信息表")
    @SysLog("通过id删除门店信息表")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_del')")
    public R removeById(@RequestBody Long[] ids) {
        return tmsStoreService.remoteStore(ids);
    }

    /**
     * 管理端修改门店密码
     */
    @Operation(summary = "管理端修改门店密码", description = "管理端修改门店密码")
    @SysLog("管理端修改门店密码")
    @PutMapping("/updateStorePwd")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_update_password')")
    public R updateStorePwd(@RequestBody TmsStoreDTO tmsStoreDTO){
        //修改客户用户账号密码
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(tmsStoreDTO.getUserId());
        userDTO.setPassword("123456");
        //更新用户的密码
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode() == 0) {
            //成功修改
            return R.ok();
        } else {
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect", null));
        }
    }

    /**
     * 禁用/启用门店
     */
    @Operation(summary = "禁用/启用门店", description = "禁用/启用门店")
    @SysLog("禁用/启用门店")
    @PutMapping("/updateStatus")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_update_status')")
    public R updateStatus(@RequestBody TmsStoreEntity tmsStore) {
        return tmsStoreService.updateStatus(tmsStore);
    }

    /**
     * 个人中心
     */
    @Operation(summary = "个人中心", description = "个人中心")
    @SysLog("个人中心")
    @GetMapping("/getInfo")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_info')")
    public R getInfo() {
        return tmsStoreService.getStoreInfo();
    }

    /**
     * 个人中心修改门店信息表
     *
     * @param tmsStore 门店信息表
     * @return R
     */
    @Operation(summary = "个人中心修改门店信息表", description = "个人中心修改门店信息表")
    @SysLog("个人中心修改门店信息表")
    @PutMapping("/info/update")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_info_edit')")
    public R updateInfoById(@RequestBody TmsStoreEntity tmsStore) {
        return R.ok(tmsStoreService.updateById(tmsStore));
    }

    /**
     * 查询门店用户所属门店ID
     */
    @Operation(summary = "查询门店用户所属门店ID", description = "查询门店用户所属门店ID")
    @SysLog("查询门店用户所属门店ID")
    @GetMapping("/store-users/{userId}")
    public R getStoreIdByUser(@PathVariable Long userId) {
        return tmsStoreService.getStoreIdByUser(userId);
    }

    /**
     * 管理端获取所有门店数据
     */
    @Operation(summary = "管理端获取所有门店数据", description = "管理端获取所有门店数据")
    @SysLog("管理端获取所有门店数据")
    @GetMapping("/getAllStore")
    public R getAllStore() {
        return R.ok(tmsStoreService.list());
    }

    /**
     * 门店端客户修改手机号验证码
     *
     * @param phone 门店端客户修改手机号验证码
     * @return R
     */
    @Operation(summary = "门店端客户修改手机号验证码", description = "门店端客户修改手机号验证码")
    @SysLog("门店端客户修改手机号验证码")
    @GetMapping("/getPhoneCode")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStore_get_phone')")
    public R getPhoneCode(@RequestParam String phone) {
        //校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(phone);
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid", null));
        }
        //处理+86开头的手机号（后面sendSmsCode方法中对于+86的不需要带上，方法已经处理过了）
        if (phone.startsWith("+86")) {
            phone = phone.substring(3);
        }
        //发送短信验证码（1：登录,2:注册,3:修改手机号）
        remoteTmsAppMobileService.sendSmsCode(phone, 3);
        return R.ok();
    }

    /**
     * 门店端客户修改手机号
     *
     * @param tmsPhoneEmailDto 门店端客户改手机号
     * @return R
     */
    @Operation(summary = "门店端客户改手机号", description = "门店端客户改手机号")
    @SysLog("门店端客户改手机号")
    @Transactional
    @PostMapping("/updatePhone")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStore_update_phone')")
    public R updatePhone(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(tmsPhoneEmailDto.getPhone());
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid", null));
        }

        String phone = tmsPhoneEmailDto.getPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if (tmsPhoneEmailDto.getPhone().startsWith("+1")) {
            phone = tmsPhoneEmailDto.getPhone().substring(2);
        }

        if (tmsPhoneEmailDto.getPhone().startsWith("+86")) {
            phone = tmsPhoneEmailDto.getPhone().substring(3);
        }

        String oldPhone = tmsPhoneEmailDto.getOldPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if (tmsPhoneEmailDto.getPhone().startsWith("+1")) {
            oldPhone = tmsPhoneEmailDto.getOldPhone().substring(2);
        }

        if (tmsPhoneEmailDto.getPhone().startsWith("+86")) {
            oldPhone = tmsPhoneEmailDto.getOldPhone().substring(3);
        }

        String code = redisTemplate.opsForValue()
                .get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + oldPhone);
        if (ObjectUtil.isNull(code) || StrUtil.isEmpty(code)) {
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid", null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid", null));
        }
        //查询当前用户，修改客户信息中的手机号
        SysUser currentUser = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId()).getData();
        //验证要修改的手机号是否已经被其他的用户注册（是否是自己的）
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByPhone(phone);
        SysUser phoneUser = result.getData();
        //根据手机号查询并且手机号对应的客户不是当前客户的-已被其他用户注册
        if (result.getCode() == 0 && ObjectUtil.isNotNull(result.getData()) && !(currentUser.getUserId().equals(phoneUser.getUserId()))) {
            throw new CustomBusinessException(LocalizedR.getMessage("phone.already.bound", null));
        }
        // 兼容修改客户手机号
        TmsStoreCustomerEntity storeCustomer = storeCustomerService.getStoreCustomerByUserId(SecurityUtils.getUser().getId());
        if(null != storeCustomer){
            TmsStoreCustomerEntity updateStoreCustomer = new TmsStoreCustomerEntity();
            updateStoreCustomer.setId(storeCustomer.getId());
            updateStoreCustomer.setPhone(phone);
            storeCustomerService.updateById(updateStoreCustomer);
        }
        //修改客户账号里的手机号
        UserDTO userDTO = new UserDTO();
        userDTO.setPhone(phone);
        //更新用户的手机号
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode() == 0) {
            //成功修改
            return R.ok();
        } else {
            throw new CustomBusinessException(LocalizedR.getMessage("update.mobile.failed", null));
        }
    }

    /**
     * 门店端客户根据旧密码修改密码
     *
     * @param tmsPhoneEmailDto 门店端客户根据旧密码修改密码
     * @return R
     */
    @Operation(summary = "门店端客户根据旧密码修改密码", description = "门店端客户根据旧密码修改密码")
    @SysLog("门店端客户根据旧密码修改密码")
    @Transactional
    @PostMapping("/updatePassword")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStore_update_info_password')")
    public R updatePassword(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) throws Exception {
        SysUser currentUser = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId()).getData();
        //解密后的原密码
        String decryptOldPassword = AESUtil.decrypt(tmsPhoneEmailDto.getOldPassword());
        //解密后的新密码
        String decryptNewPassword = AESUtil.decrypt(tmsPhoneEmailDto.getNewPassword());

        //校验原密码
        if (!ENCODER.matches(decryptOldPassword, currentUser.getPassword())) {
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect", null));
        }

        //修改客户用户账号密码
        UserDTO userDTO = new UserDTO();
        userDTO.setPassword(decryptNewPassword);
        //更新用户的密码
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode() == 0) {
            //成功修改
            return R.ok();
        } else {
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect", null));
        }
    }

    /**
     * 客户端客户修改邮箱验证码
     */
    @Operation(summary = "客户端客户修改邮箱验证码", description = "客户端客户修改邮箱验证码")
    @SysLog("客户端客户修改邮箱验证码")
    @GetMapping("/getEmailCode")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStore_get_email')")
    public R getEmailCode(String email) {
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if (!pattern.matcher(email).matches()) {
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid", null));
        }
        //生成一个唯一的验证码
        String code = generateNumericCodeFromUUID(6);

        //将验证码存储到Redis中，并设置过期时间为5分钟
        redisTemplate.opsForValue().set("customer:updateEmail:" + SecurityUtils.getUser().getId(), code,
                300, TimeUnit.SECONDS);

        String emailSubject = "Modify email verification";
        String emailContent = "<p>You are modifying your email address. The verification code is：" + "</p>" + "<h3>" + code + "</h3>"
                + "<br>" + "<p>" + "The validity period of the verification code is 5 minutes. Please verify it in time" + "</p>";

        boolean isSent = MailSenderUtil.sendMail(email, emailSubject, emailContent);
        if (isSent) {
            return R.ok();
        } else {
            return R.failed();
        }
    }


    /**
     * 客户端客户修改邮箱
     *
     * @param tmsPhoneEmailDto 客户端客户修改邮箱
     * @return R
     */
    @Operation(summary = "客户端客户修改邮箱", description = "客户端客户修改邮箱")
    @SysLog("客户端客户修改邮箱")
    @PostMapping("/updateEmail")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStore_update_email')")
    public R updateEmail(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if (!pattern.matcher(tmsPhoneEmailDto.getEmail()).matches()) {
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid", null));
        }

        //校验邮箱是否已被其他人绑定
        SysUser customerByEmail = remoteTmsUpmsService.getCustomerUserByEmail(tmsPhoneEmailDto.getEmail()).getData();
        SysUser data = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId()).getData();
        if (ObjectUtil.isNotNull(customerByEmail) && !customerByEmail.getUserId().equals(data.getUserId())) {
            throw new CustomBusinessException(LocalizedR.getMessage("email.already.registered", null));
        }

        String code = redisTemplate.opsForValue().get("customer:updateEmail:" + SecurityUtils.getUser().getId());
        if (ObjectUtil.isNull(code) || StrUtil.isEmpty(code)) {
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid", null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid", null));
        }
        // 兼容修改客户邮箱
        TmsStoreCustomerEntity storeCustomer = storeCustomerService.getStoreCustomerByUserId(SecurityUtils.getUser().getId());
        if(null != storeCustomer){
            TmsStoreCustomerEntity updateStoreCustomer = new TmsStoreCustomerEntity();
            updateStoreCustomer.setId(storeCustomer.getId());
            updateStoreCustomer.setEmail(tmsPhoneEmailDto.getEmail());
            storeCustomerService.updateById(updateStoreCustomer);
        }
        //修改用户信息中的邮箱
        UserDTO userDTO = new UserDTO();
        userDTO.setEmail(tmsPhoneEmailDto.getEmail());
        //更新用户的邮箱
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode() == 0) {
            //成功修改
            return R.ok();
        } else {
            throw new CustomBusinessException(LocalizedR.getMessage("update.email.failed", null));
        }
    }

    /**
     * 导出excel 表格
     *
     * @param tmsStore 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStore_export')")
    public List<TmsStoreExcelVo> export(@RequestBody TmsStorePageVo tmsStore) {
        return tmsStoreService.exportStore(tmsStore);
    }

    /**
     * 校验带国家代码的手机号码
     * 示例：+8613812345678（中国），+***********（美国）
     */
    public static boolean isValidInternationalPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }
        // 基本格式校验：以 + 开头，后面是数字，长度 8~20 之间
        if (!phone.matches("^\\+[0-9]{8,20}$")) {
            return false;
        }
        // 这里只实现中国和美国的校验
        return isSupportedFormat(phone);
    }

    /**
     * 基于国家代码做基础规则校验
     */
    private static boolean isSupportedFormat(String phone) {
        // 中国大陆，11位手机号
        if (phone.startsWith("+86")) {
            return phone.length() == 14 && phone.matches("^\\+86(1[3-9][0-9]{9})$");
        }
        // 美国/加拿大，10位号码
        else if (phone.startsWith("+1")) {
            return phone.length() == 12 && phone.matches("^\\+1[2-9][0-9]{9}$");
        }
        return false;
    }

    /**
     * 生成指定长度的数字验证码
     *
     * @param length 验证码长度
     * @return 生成的数字验证码
     */
    private static String generateNumericCodeFromUUID(int length) {
        UUID uuid = UUID.randomUUID();
        // 去掉连字符
        String uuidStr = uuid.toString().replace("-", "");
        StringBuilder code = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 每次取两个字符
            int index = i * 2;
            String substring = uuidStr.substring(index, index + 2);
            // 转换为0到9之间的数字
            int digit = Integer.parseInt(substring, 16) % 10;
            code.append(digit);
        }
        return code.toString();
    }
}
