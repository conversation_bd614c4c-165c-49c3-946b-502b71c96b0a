package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreMessageTraceEntity;
import com.jygjexp.jynx.tms.service.TmsStoreMessageTraceService;
import com.jygjexp.jynx.tms.vo.StoreMessageTraceVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 门店消息中心
 *
 * <AUTHOR>
 * @date 2025-07-14 17:52:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreMessageTrace" )
@Tag(description = "tmsStoreMessageTrace" , name = "门店消息中心管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreMessageTraceController {

    private final TmsStoreMessageTraceService service;
    /**
     * 分页查询 门店快递通知中心
     * @param page 分页对象
     * @param
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_storeMessageTrace_view')" )
    public R selectPage(@ParameterObject Page page) {
        return R.ok(service.selectPage(page));
    }

    @Operation(summary = "未读总数" , description = "未读总数" )
    @GetMapping("/counts" )
    @PreAuthorize("@pms.hasPermission('tms_storeMessageTrace_view')" )
    public R getCounts() {
        return R.ok(service.getCounts());
    }

    /**
     * 通过id查询门店消息中心
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    // @PreAuthorize("@pms.hasPermission('tms_storeMessageTrace_query')" )
    public R getMessageTraceById(@PathVariable("id" ) Long id) {
        TmsStoreMessageTraceEntity beanDB = service.getById(id);
        StoreMessageTraceVO beanVO = BeanUtil.toBean(beanDB, StoreMessageTraceVO.class);
        return R.ok(beanVO);
    }

    @Operation(summary = "修改已读" , description = "修改已读" )
    @PutMapping("/change/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_storeMessageTrace_update')" )
    public R changeReadFlag(@PathVariable("id" ) Long id) {
        TmsStoreMessageTraceEntity updateBean = new TmsStoreMessageTraceEntity();
        updateBean.setId(id);
        updateBean.setReadFlag(StoreEnums.StoreMessageTrace.ReadFlag.READ.getValue());
        return R.ok(service.updateById(updateBean));
    }
    /**
     * 通过id删除门店消息中心
     * @param id id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店消息中心" , description = "通过id删除门店消息中心")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_storeMessageTrace_delete')" )
    public R removeById(@RequestParam("id") Long id) {
        return R.ok(service.removeById(id));
    }
}
