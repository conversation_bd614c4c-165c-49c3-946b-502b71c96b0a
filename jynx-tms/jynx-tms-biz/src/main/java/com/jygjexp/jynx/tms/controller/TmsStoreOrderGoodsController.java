package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderGoodsEntity;
import com.jygjexp.jynx.tms.service.TmsStoreOrderGoodsService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 门店订单货物信息
 *
 * <AUTHOR>
 * @date 2025-07-14 17:39:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreOrderGoods" )
@Tag(description = "tmsStoreOrderGoods" , name = "门店订单货物信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreOrderGoodsController {

    private final  TmsStoreOrderGoodsService tmsStoreOrderGoodsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreOrderGoods 门店订单货物信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_view')" )
    public R getTmsStoreOrderGoodsPage(@ParameterObject Page page, @ParameterObject TmsStoreOrderGoodsEntity tmsStoreOrderGoods) {
        LambdaQueryWrapper<TmsStoreOrderGoodsEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStoreOrderGoodsService.page(page, wrapper));
    }


    /**
     * 通过id查询门店订单货物信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreOrderGoodsService.getById(id));
    }

    /**
     * 新增门店订单货物信息
     * @param tmsStoreOrderGoods 门店订单货物信息
     * @return R
     */
    @Operation(summary = "新增门店订单货物信息" , description = "新增门店订单货物信息" )
    @SysLog("新增门店订单货物信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_add')" )
    public R save(@RequestBody TmsStoreOrderGoodsEntity tmsStoreOrderGoods) {
        return R.ok(tmsStoreOrderGoodsService.save(tmsStoreOrderGoods));
    }

    /**
     * 修改门店订单货物信息
     * @param tmsStoreOrderGoods 门店订单货物信息
     * @return R
     */
    @Operation(summary = "修改门店订单货物信息" , description = "修改门店订单货物信息" )
    @SysLog("修改门店订单货物信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_edit')" )
    public R updateById(@RequestBody TmsStoreOrderGoodsEntity tmsStoreOrderGoods) {
        return R.ok(tmsStoreOrderGoodsService.updateById(tmsStoreOrderGoods));
    }

    /**
     * 通过id删除门店订单货物信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店订单货物信息" , description = "通过id删除门店订单货物信息" )
    @SysLog("通过id删除门店订单货物信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreOrderGoodsService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStoreOrderGoods 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderGoods_export')" )
    public List<TmsStoreOrderGoodsEntity> export(TmsStoreOrderGoodsEntity tmsStoreOrderGoods,Long[] ids) {
        return tmsStoreOrderGoodsService.list(Wrappers.lambdaQuery(tmsStoreOrderGoods).in(ArrayUtil.isNotEmpty(ids), TmsStoreOrderGoodsEntity::getId, ids));
    }
}
