package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderPkgPayEntity;
import com.jygjexp.jynx.tms.service.TmsStoreOrderPkgPayService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 快递订单包裹赔付表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:48:01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreOrderPkgPay" )
@Tag(description = "tmsStoreOrderPkgPay" , name = "快递订单包裹赔付表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreOrderPkgPayController {

    private final  TmsStoreOrderPkgPayService tmsStoreOrderPkgPayService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreOrderPkgPay 快递订单包裹赔付表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_view')" )
    public R getTmsStoreOrderPkgPayPage(@ParameterObject Page page, @ParameterObject TmsStoreOrderPkgPayEntity tmsStoreOrderPkgPay) {
        LambdaQueryWrapper<TmsStoreOrderPkgPayEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStoreOrderPkgPayService.page(page, wrapper));
    }


    /**
     * 通过id查询快递订单包裹赔付表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreOrderPkgPayService.getById(id));
    }

    /**
     * 新增快递订单包裹赔付表
     * @param tmsStoreOrderPkgPay 快递订单包裹赔付表
     * @return R
     */
    @Operation(summary = "新增快递订单包裹赔付表" , description = "新增快递订单包裹赔付表" )
    @SysLog("新增快递订单包裹赔付表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_add')" )
    public R save(@RequestBody TmsStoreOrderPkgPayEntity tmsStoreOrderPkgPay) {
        return tmsStoreOrderPkgPayService.savePkgPay(tmsStoreOrderPkgPay);
    }

    /**
     * 修改快递订单包裹赔付表
     * @param tmsStoreOrderPkgPay 快递订单包裹赔付表
     * @return R
     */
    @Operation(summary = "修改快递订单包裹赔付表" , description = "修改快递订单包裹赔付表" )
    @SysLog("修改快递订单包裹赔付表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_edit')" )
    public R updateById(@RequestBody TmsStoreOrderPkgPayEntity tmsStoreOrderPkgPay) {
        return R.ok(tmsStoreOrderPkgPayService.updateById(tmsStoreOrderPkgPay));
    }

    /**
     * 通过id删除快递订单包裹赔付表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除快递订单包裹赔付表" , description = "通过id删除快递订单包裹赔付表" )
    @SysLog("通过id删除快递订单包裹赔付表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreOrderPkgPayService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStoreOrderPkgPay 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrderPkgPay_export')" )
    public List<TmsStoreOrderPkgPayEntity> export(TmsStoreOrderPkgPayEntity tmsStoreOrderPkgPay,Long[] ids) {
        return tmsStoreOrderPkgPayService.list(Wrappers.lambdaQuery(tmsStoreOrderPkgPay).in(ArrayUtil.isNotEmpty(ids), TmsStoreOrderPkgPayEntity::getId, ids));
    }
}