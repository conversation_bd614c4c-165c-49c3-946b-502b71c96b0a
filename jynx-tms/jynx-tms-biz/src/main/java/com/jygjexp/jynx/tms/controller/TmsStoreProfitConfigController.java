package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreProfitConfigDetailEntity;
import com.jygjexp.jynx.tms.service.TmsStoreProfitConfigService;
import com.jygjexp.jynx.tms.vo.TmsStoreProfitConfigPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 服务商利润配置
 *
 * <AUTHOR>
 * @date 2025-08-26 10:46:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreProfitConfig" )
@Tag(description = "tmsStoreProfitConfig" , name = "服务商利润配置管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreProfitConfigController {

    private final  TmsStoreProfitConfigService tmsStoreProfitConfigService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStoreProfitConfig 服务商利润配置
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_view')" )
    public R getTmsStoreProfitConfigPage(@ParameterObject Page page, @ParameterObject TmsStoreProfitConfigPageVo tmsStoreProfitConfig) {
        return R.ok(tmsStoreProfitConfigService.search(page, tmsStoreProfitConfig));
    }

    /**
     * 通过id查询服务商利润配置
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStoreProfitConfigService.getByIdDeatil(id));
    }

    /**
     * 新增服务商利润配置
     * @param tmsStoreProfitConfig 服务商利润配置
     * @return R
     */
    @Operation(summary = "新增服务商利润配置" , description = "新增服务商利润配置" )
    @SysLog("新增服务商利润配置" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_add')" )
    public R save(@RequestBody TmsStoreProfitConfigEntity tmsStoreProfitConfig) {
        return R.ok(tmsStoreProfitConfigService.saveDeep(tmsStoreProfitConfig));
    }

    /**
     * 修改服务商利润配置
     * @param tmsStoreProfitConfig 服务商利润配置
     * @return R
     */
    @Operation(summary = "修改服务商利润配置" , description = "修改服务商利润配置" )
    @SysLog("修改服务商利润配置" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_edit')" )
    public R updateById(@RequestBody TmsStoreProfitConfigEntity tmsStoreProfitConfig) {
        return R.ok(tmsStoreProfitConfigService.updateDeep(tmsStoreProfitConfig));
    }

    /**
     * 通过id删除服务商利润配置
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商利润配置" , description = "通过id删除服务商利润配置" )
    @SysLog("通过id删除服务商利润配置" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStoreProfitConfigService.removeDeep(ids));
    }

    /**
     * 通过id删除服务商利润配置子表数据
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务商利润配置子表数据" , description = "通过id删除服务商利润配置子表数据" )
    @SysLog("通过id删除服务商利润配置子表数据" )
    @DeleteMapping("/child")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_del')" )
    public R removeChild(@RequestBody Long[] ids) {
        return R.ok(tmsStoreProfitConfigService.removeChild(ids));
    }


    @PostMapping("/updateStatus")
    @Operation(summary = "启用/停用服务商利润配置")
    public R<Boolean> updateStatus(@RequestParam Long id, @RequestParam Integer isValid) {
        boolean result = tmsStoreProfitConfigService.updateStatus(id, isValid);
        return R.ok(result);
    }
    /**
     * 导出excel 表格
     * @param tmsStoreProfitConfig 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
/*    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStoreProfitConfig_export')" )
    public List<TmsStoreProfitConfigEntity> export(TmsStoreProfitConfigEntity tmsStoreProfitConfig,Long[] ids) {
        return tmsStoreProfitConfigService.list(Wrappers.lambdaQuery(tmsStoreProfitConfig).in(ArrayUtil.isNotEmpty(ids), TmsStoreProfitConfigEntity::getId, ids));
    }*/
}