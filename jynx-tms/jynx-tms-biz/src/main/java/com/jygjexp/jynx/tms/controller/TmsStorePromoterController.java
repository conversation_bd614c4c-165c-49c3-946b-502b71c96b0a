package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsStorePromoterQueryDTO;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCommissionDTO;
import com.jygjexp.jynx.tms.entity.TmsStorePromoterEntity;
import com.jygjexp.jynx.tms.service.TmsStorePromoterService;
import com.jygjexp.jynx.tms.utils.SelectUtil;
import com.jygjexp.jynx.tms.vo.TmsStorePromoterExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStorePromoterOrderExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionOrderVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 推广人表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:53:29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorePromoter")
@Tag(description = "tmsStorePromoter", name = "推广人表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorePromoterController {

    private final TmsStorePromoterService tmsStorePromoterService;

    /**
     * 分页查询
     *
     * @param page             分页对象
     * @param tmsStorePromoter 推广人表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_view')")
    public R getTmsStorePromoterPage(@ParameterObject Page page, @ParameterObject TmsStorePromoterQueryDTO tmsStorePromoter) {
        LambdaQueryWrapper<TmsStorePromoterEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like((StrUtil.isNotBlank(tmsStorePromoter.getPromoterName())), TmsStorePromoterEntity::getPromoterName, tmsStorePromoter.getPromoterName())
                .eq((tmsStorePromoter.getStatus() != null), TmsStorePromoterEntity::getStatus, tmsStorePromoter.getStatus())
                .between((tmsStorePromoter.getCreateStartTime() != null && tmsStorePromoter.getCreateEndTime() != null),
                        TmsStorePromoterEntity::getCreateTime, tmsStorePromoter.getCreateStartTime(), tmsStorePromoter.getCreateEndTime())
                .orderByDesc(TmsStorePromoterEntity::getCreateTime);
        return R.ok(tmsStorePromoterService.page(page, wrapper));
    }


    /**
     * 通过id查询推广人表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_view')")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsStorePromoterService.getById(id));
    }

    /**
     * 新增推广人表
     *
     * @param tmsStorePromoter 推广人表
     * @return R
     */
    @Operation(summary = "新增推广人表", description = "新增推广人表")
    @SysLog("新增推广人表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_add')")
    public R save(@RequestBody TmsStorePromoterEntity tmsStorePromoter) {
        return R.ok(tmsStorePromoterService.savePromoter(tmsStorePromoter));
    }

    /**
     * 修改推广人表
     *
     * @param tmsStorePromoter 推广人表
     * @return R
     */
    @Operation(summary = "修改推广人表", description = "修改推广人表")
    @SysLog("修改推广人表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_edit')")
    public R updateById(@RequestBody TmsStorePromoterEntity tmsStorePromoter) {
        return R.ok(tmsStorePromoterService.updatePromoter(tmsStorePromoter));
    }

    /**
     * 通过id删除推广人表
     *
     * @return R
     */
    @Operation(summary = "通过id删除推广人表", description = "通过id删除推广人表")
    @SysLog("通过id删除推广人表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_del')")
    public R removeById(@PathVariable("id") Long id) {
        return R.ok(tmsStorePromoterService.removePromoter(id));
    }

    /**
     * 启用/禁用推广人
     */
    @Operation(summary = "启用/禁用推广人", description = "启用/禁用推广人")
    @SysLog("启用/禁用推广人")
    @PostMapping("/status")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_status')")
    public R updateStatus(@RequestBody TmsStorePromoterEntity tmsStorePromoter) {
        if (tmsStorePromoter.getStatus() != null) {
            return R.ok(tmsStorePromoterService.update(new LambdaUpdateWrapper<TmsStorePromoterEntity>()
                    .set(TmsStorePromoterEntity::getStatus, tmsStorePromoter.getStatus())
                    .eq(TmsStorePromoterEntity::getId, tmsStorePromoter.getId())));
        }
        return R.failed();
    }

    /**
     * 获取所有有效的推广人
     */
    @Operation(summary = "获取所有有效的推广人", description = "获取所有有效的推广人")
    @SysLog("获取所有有效的推广人")
    @GetMapping("/allValidPromoter")
    public R getAllValidPromoter() {
        return R.ok(tmsStorePromoterService.list(new LambdaQueryWrapper<TmsStorePromoterEntity>()
                .eq(TmsStorePromoterEntity::getStatus, 1)));
    }

    /**
     * 分页查询推送人的周期数据
     */
    @Operation(summary = "分页查询推送人的周期数据", description = "分页查询推送人的周期数据")
    @SysLog("分页查询推送人的周期数据")
    @GetMapping("/page/pushCycle")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_pushCycle')")
    public R<IPage<TmsStorePromotionOrderVo>> getPushCyclePage(@ParameterObject Page page, @ParameterObject TmsStorePromotionOrderPageVo tmsStorePromoter) {
        return R.ok(tmsStorePromoterService.getPushCyclePage(page, tmsStorePromoter));
    }

    /**
     * 佣金结算
     */
    @Operation(summary = "佣金结算", description = "佣金结算")
    @SysLog("佣金结算")
    @PostMapping("/settle")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_settle')")
    public R settle(@RequestBody TmsStorePromotionCommissionDTO tmsStorePromotionOrder) {
        return R.ok(tmsStorePromoterService.settle(tmsStorePromotionOrder));
    }

    /**
     * 获取推广人周期详情数据
     */
    @Operation(summary = "获取推广人周期详情数据", description = "获取推广人周期详情数据")
    @SysLog("获取推广人周期详情数据")
    @PostMapping("/getPromoterPeriodDetail")
    @PreAuthorize("@pms.hasPermission('tms_storePromoter_getDetail')")
    public R<TmsStorePromotionOrderVo> getPromoterPeriodDetail(@RequestBody TmsStorePromotionCommissionDTO tmsStorePromotionOrder) {
        return R.ok(tmsStorePromoterService.getPromoterPeriodDetail(tmsStorePromotionOrder));
    }

    /**
     * 导出推广人
     *
     * @param tmsStorePromoter 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @Operation(summary = "导出推广人", description = "导出推广人")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoter_export')")
    public List<TmsStorePromoterExcelVo> export(@RequestBody TmsStorePromoterQueryDTO tmsStorePromoter) {
        return tmsStorePromoterService.exportPromoter(tmsStorePromoter);
    }

    /**
     * 导出结算列表
     *
     * @param tmsStorePromoter 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/exportPromoter")
    @Operation(summary = "导出结算列表", description = "导出结算列表")
    @PreAuthorize("@pms.hasPermission('tms_storePromoter_export_settle')")
    public List<TmsStorePromoterOrderExcelVo> export(@RequestBody TmsStorePromotionOrderPageVo tmsStorePromoter) {
        return tmsStorePromoterService.exportPromoterSettle(tmsStorePromoter);
    }
}