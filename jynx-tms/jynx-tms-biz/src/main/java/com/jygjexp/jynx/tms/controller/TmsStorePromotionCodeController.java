package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeDTO;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionCodeEntity;
import com.jygjexp.jynx.tms.service.TmsStorePromotionCodeService;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionCodeExcelVo;
import com.jygjexp.jynx.tms.dto.TmsStorePromotionCodeQueryDTO;
import com.jygjexp.jynx.tms.vo.TmsStorePromotionCodePageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 推广码表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorePromotionCode")
@Tag(description = "tmsStorePromotionCode", name = "推广码表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorePromotionCodeController {

    private final TmsStorePromotionCodeService tmsStorePromotionCodeService;

    /**
     * 分页查询
     *
     * @param page                  分页对象
     * @param tmsStorePromotionCode 推广码表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_view')")
    public R<IPage<TmsStorePromotionCodePageVo>> getTmsStorePromotionCodePage(@ParameterObject Page page, @ParameterObject TmsStorePromotionCodeQueryDTO tmsStorePromotionCode) {
        return R.ok(tmsStorePromotionCodeService.promotionCodePage(page, tmsStorePromotionCode));
    }


    /**
     * 通过id查询推广码详情
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询推广码详情", description = "通过id查询推广码详情")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_view')")
    public R getDetailById(@PathVariable("id") Long id) {
        return R.ok(tmsStorePromotionCodeService.getDetailById(id));
    }

    /**
     * 新增推广码表
     *
     * @param tmsStorePromotionCode 推广码表
     * @return R
     */
    @Operation(summary = "新增推广码表", description = "新增推广码表")
    @SysLog("新增推广码表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_add')")
    public R save(@RequestBody TmsStorePromotionCodeDTO tmsStorePromotionCode) {
        return R.ok(tmsStorePromotionCodeService.savePromotionCode(tmsStorePromotionCode));
    }

    /**
     * 修改推广码表
     *
     * @param tmsStorePromotionCode 推广码表
     * @return R
     */
    @Operation(summary = "修改推广码表", description = "修改推广码表")
    @SysLog("修改推广码表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_edit')")
    public R updateById(@RequestBody TmsStorePromotionCodeDTO tmsStorePromotionCode) {
        return R.ok(tmsStorePromotionCodeService.updatePromotionCode(tmsStorePromotionCode));
    }

    /**
     * 启用/禁用推广码
     */
    @Operation(summary = "启用/禁用推广码", description = "启用/禁用推广码")
    @SysLog("启用/禁用推广码")
    @PutMapping("/enable")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_status')")
    public R updateEnable(@RequestBody TmsStorePromotionCodeEntity tmsStorePromotionCode) {
        if (tmsStorePromotionCode.getStatus() != null) {
            return R.ok(tmsStorePromotionCodeService.update(new LambdaUpdateWrapper<TmsStorePromotionCodeEntity>()
                    .eq(TmsStorePromotionCodeEntity::getId, tmsStorePromotionCode.getId())
                    .set(TmsStorePromotionCodeEntity::getStatus, tmsStorePromotionCode.getStatus())));
        }
        return R.failed();
    }

    /**
     * 通过id删除推广码表
     */
    @Operation(summary = "通过id删除推广码表", description = "通过id删除推广码表")
    @SysLog("通过id删除推广码表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_del')")
    public R removeById(@PathVariable("id") Long id) {
        return R.ok(tmsStorePromotionCodeService.removePromotionCode(id));
    }


    /**
     * 导出excel 表格
     *
     * @param tmsStorePromotionCode 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "导出excel 表格", description = "导出excel 表格")
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCode_export')")
    public List<TmsStorePromotionCodeExcelVo> export(@RequestBody TmsStorePromotionCodeQueryDTO tmsStorePromotionCode) {
        return tmsStorePromotionCodeService.export(tmsStorePromotionCode);
    }
}