package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import com.jygjexp.jynx.tms.entity.TmsTaxRateEntity;
import com.jygjexp.jynx.tms.service.TmsTaxRateService;
import com.jygjexp.jynx.tms.vo.excel.TmsTaxRateExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 税率维护
 *
 * <AUTHOR>
 * @date 2025-07-11 10:33:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsTaxRate" )
@Tag(description = "tmsTaxRate" , name = "税率维护管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsTaxRateController {

    private final  TmsTaxRateService tmsTaxRateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsTaxRate 税率维护
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_view')" )
    public R getTmsTaxRatePage(@ParameterObject Page page, @ParameterObject TmsTaxRateEntity tmsTaxRate) {
        LambdaQueryWrapper<TmsTaxRateEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(tmsTaxRate.getIsValid()), TmsTaxRateEntity::getIsValid, tmsTaxRate.getIsValid());
        wrapper.eq(StrUtil.isNotBlank(tmsTaxRate.getProvince()), TmsTaxRateEntity::getProvince, tmsTaxRate.getProvince());
        wrapper.orderByDesc(TmsTaxRateEntity::getCreateTime);
        return R.ok(tmsTaxRateService.page(page, wrapper));
    }


    /**
     * 通过id查询税率维护
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsTaxRateService.getById(id));
    }

    /**
     * 新增税率维护
     * @param tmsTaxRate 税率维护
     * @return R
     */
    @Operation(summary = "新增税率维护" , description = "新增税率维护" )
    @SysLog("新增税率维护" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_add')" )
    public R save(@RequestBody TmsTaxRateEntity tmsTaxRate) {
        if (!checkName(tmsTaxRate)) {
            return R.failed("税率维护省份不可重复！");
        }
        return R.ok(tmsTaxRateService.save(tmsTaxRate));
    }

    /**
     * 修改税率维护
     * @param tmsTaxRate 税率维护
     * @return R
     */
    @Operation(summary = "修改税率维护" , description = "修改税率维护" )
    @SysLog("修改税率维护" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_edit')" )
    public R updateById(@RequestBody TmsTaxRateEntity tmsTaxRate) {
        if (!checkName(tmsTaxRate)) {
            return R.failed("税率维护省份不可重复！");
        }
        return R.ok(tmsTaxRateService.updateById(tmsTaxRate));
    }

    // 判断税率维护省份不可重复
    public Boolean checkName(TmsTaxRateEntity tmsTaxRate) {
        LambdaQueryWrapper<TmsTaxRateEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TmsTaxRateEntity::getProvince, tmsTaxRate.getProvince());
        queryWrapper.ne(ObjectUtil.isNotNull(tmsTaxRate.getId()),TmsTaxRateEntity::getId, tmsTaxRate.getId());
        TmsTaxRateEntity byName = tmsTaxRateService.getOne(queryWrapper,false);
        if (Objects.nonNull(byName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 通过id删除税率维护
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除税率维护" , description = "通过id删除税率维护" )
    @SysLog("通过id删除税率维护" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsTaxRateService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsTaxRate 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsTaxRate_export')" )
    public List<TmsTaxRateExcelVo> export(TmsTaxRateEntity tmsTaxRate, Long[] ids) {
        return tmsTaxRateService.taxRateExport(tmsTaxRate,ids);
    }
}