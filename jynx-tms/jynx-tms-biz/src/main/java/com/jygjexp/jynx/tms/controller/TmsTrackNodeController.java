package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsTrackNodeEntity;
import com.jygjexp.jynx.tms.service.TmsTrackNodeService;
import com.jygjexp.jynx.tms.vo.TmsTrackNodePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 轨迹节点维护
 *
 * <AUTHOR>
 * @date 2025-05-16 18:03:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsTrackNode" )
@Tag(description = "tmsTrackNode" , name = "轨迹节点维护管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsTrackNodeController {

    private final  TmsTrackNodeService tmsTrackNodeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 轨迹节点维护
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_view')" )
    public R getTmsTrackNodePage(@ParameterObject Page page, @ParameterObject TmsTrackNodePageVo vo) {
        return R.ok(tmsTrackNodeService.search(page, vo));
    }


    /**
     * 通过id查询轨迹节点维护
     * @param nodeId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{nodeId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_view')" )
    public R getById(@PathVariable("nodeId" ) Long nodeId) {
        return R.ok(tmsTrackNodeService.getById(nodeId));
    }

    /**
     * 新增轨迹节点维护
     * @param tmsTrackNode 轨迹节点维护
     * @return R
     */
    @Operation(summary = "新增轨迹节点维护" , description = "新增轨迹节点维护" )
    @SysLog("新增轨迹节点维护" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_add')" )
    public R save(@RequestBody TmsTrackNodeEntity tmsTrackNode) {
        return tmsTrackNodeService.saveTrackNode(tmsTrackNode);
    }

    /**
     * 修改轨迹节点维护
     * @param tmsTrackNode 轨迹节点维护
     * @return R
     */
    @Operation(summary = "修改轨迹节点维护" , description = "修改轨迹节点维护" )
    @SysLog("修改轨迹节点维护" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_edit')" )
    public R updateById(@RequestBody TmsTrackNodeEntity tmsTrackNode) {
        return tmsTrackNodeService.updateTrackNode(tmsTrackNode);
    }

    /**
     * 通过id删除轨迹节点维护
     * @param ids nodeId列表
     * @return R
     */
    @Operation(summary = "通过id删除轨迹节点维护" , description = "通过id删除轨迹节点维护" )
    @SysLog("通过id删除轨迹节点维护" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsTrackNodeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsTrackNode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsTrackNode_export')" )
    public List<TmsTrackNodeEntity> export(TmsTrackNodeEntity tmsTrackNode,Long[] ids) {
        return tmsTrackNodeService.list(Wrappers.lambdaQuery(tmsTrackNode).in(ArrayUtil.isNotEmpty(ids), TmsTrackNodeEntity::getNodeId, ids));
    }


    // 查询全部轨迹节点
    @Operation(summary = "查询全部轨迹节点" , description = "查询全部轨迹节点" )
    @GetMapping("/listTrackNode")
    public R listTrackNode() {
        LambdaQueryWrapper<TmsTrackNodeEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(TmsTrackNodeEntity::getCreateTime);
        return R.ok(tmsTrackNodeService.list(queryWrapper));
    }


}