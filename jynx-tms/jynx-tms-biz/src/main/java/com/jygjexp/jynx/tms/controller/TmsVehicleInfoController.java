package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsVehicleDriverRelationEntity;
import com.jygjexp.jynx.tms.entity.TmsVehicleInfoEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsDriverService;
import com.jygjexp.jynx.tms.service.TmsVehicleDriverRelationService;
import com.jygjexp.jynx.tms.vo.TmsVehicleInfoPageVo;
import com.jygjexp.jynx.tms.vo.TmsVehicleSaveVo;
import com.jygjexp.jynx.tms.vo.excel.TmsVehicleInfoExcelVo;
import com.jygjexp.jynx.tms.service.TmsVehicleInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 车辆信息
 *
 * <AUTHOR>
 * @date 2025-02-24 18:41:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsVehicleInfo" )
@Tag(description = "tmsVehicleInfo" , name = "车辆信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsVehicleInfoController {

    private final  TmsVehicleInfoService tmsVehicleInfoService;
    private final TmsDriverService tmsDriverService;
    private final TmsVehicleDriverRelationService vehicleDriverRelationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param entity 卡派车辆信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_view')" )
    public R getTmsVehicleInfoPage(@ParameterObject Page page, @ParameterObject TmsVehicleInfoPageVo entity) {
        return R.ok(tmsVehicleInfoService.search(page, entity));
    }


    /**
     * 通过id查询卡派车辆信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsVehicleInfoService.getById(id));
    }

    /**
     * 新增卡派车辆信息
     * @param tmsVehicleInfo 卡派车辆信息
     * @return R
     */
    @Operation(summary = "新增车辆信息" , description = "新增车辆信息" )
    @SysLog("新增车辆信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_add')" )
    public R save(@RequestBody TmsVehicleSaveVo tmsVehicleInfo) {
        return R.ok(tmsVehicleInfoService.saveVehicleInfo(tmsVehicleInfo));
    }

    /**
     * 修改卡派车辆信息
     * @param tmsVehicleInfo 卡派车辆信息
     * @return R
     */
    @Operation(summary = "修改车辆信息" , description = "修改车辆信息" )
    @SysLog("修改卡派车辆信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_edit')" )
    public R updateById(@RequestBody TmsVehicleSaveVo tmsVehicleInfo) {
        return R.ok(tmsVehicleInfoService.updateVehicleInfo(tmsVehicleInfo));
    }

    /**
     * 通过id删除卡派车辆信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派车辆信息" , description = "通过id删除卡派车辆信息" )
    @SysLog("通过id删除卡派车辆信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_del')" )
    public R removeById(@RequestBody Long[] ids) {
        List<Long> vehicleIds = CollUtil.toList(ids);

        // 先删除车辆-司机关联关系
        vehicleDriverRelationService.remove(
                new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                        .in(TmsVehicleDriverRelationEntity::getVehicleId, vehicleIds)
        );

        // 再删除车辆信息
        return R.ok(tmsVehicleInfoService.removeBatchByIds(vehicleIds));
    }

    /**
     * 查询空闲中和运送中司机信息
     * @return
     */
    @Operation(summary = "查询启用司机车辆列表" , description = "查询启用司机车辆列表" )
    @GetMapping("/listDriverInfo")
    @Inner(value = false)
    public R listDriverInfo(){
        return R.ok(tmsDriverService.listDriverInfo());
    }

    /**
     * 导出excel 表格
     * @param tmsVehicleInfo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_tmsVehicleInfo_export')" )
    public List<TmsVehicleInfoExcelVo> export(TmsVehicleInfoPageVo tmsVehicleInfo, Long[] ids) {
        return tmsVehicleInfoService.getExcel(tmsVehicleInfo, ids);
    }
}