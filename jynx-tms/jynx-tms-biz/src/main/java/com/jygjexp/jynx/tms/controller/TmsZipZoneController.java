package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsGeoDataEntity;
import com.jygjexp.jynx.tms.entity.TmsZipZoneEntity;
import com.jygjexp.jynx.tms.enums.EntrustedOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsGeoDataMapper;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.service.TmsZipZoneService;
import com.jygjexp.jynx.tms.vo.TmsZipZonePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * TMS区域邮编分区
 *
 * <AUTHOR>
 * @date 2025-03-20 11:10:21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsZipZone" )
@Tag(description = "tmsZipZone" , name = "TMS区域邮编分区管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsZipZoneController {

    private final  TmsZipZoneService tmsZipZoneService;
    private final TmsOrderTrackService orderTrackService;
    private final TmsGeoDataMapper geoDataMapper;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsZipZone TMS区域邮编分区
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_view')" )
    public R getTmsZipZonePage(@ParameterObject Page page, @ParameterObject TmsZipZonePageVo tmsZipZone) {
        return R.ok(tmsZipZoneService.search(page, tmsZipZone));
    }


    /**
     * 通过id查询TMS区域邮编分区
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsZipZoneService.getById(id));
    }

    /**
     * 通过仓库id查询覆盖邮编点位数据
     * @param id id
     * @return R
     */
    @Operation(summary = "通过仓库id查询覆盖邮编点位数据" , description = "通过仓库id查询覆盖邮编点位数据" )
    @GetMapping("listGeoData/{id}" )
    public R listGeoData(@PathVariable("id" ) Long id) {
        return tmsZipZoneService.listGeoData(id);
    }

    /**
     * 新增TMS区域邮编分区
     * @param tmsZipZone TMS区域邮编分区
     * @return R
     */
    @Operation(summary = "新增TMS区域邮编分区" , description = "新增TMS区域邮编分区" )
    @SysLog("新增TMS区域邮编分区" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_add')" )
    public R save(@RequestBody TmsZipZoneEntity tmsZipZone) {
        // 判断路线编码是否已存在
        if (Objects.nonNull(tmsZipZoneService.getOne(Wrappers.<TmsZipZoneEntity>lambdaQuery()
                        .eq(TmsZipZoneEntity::getRouteNumber, tmsZipZone.getRouteNumber())
                .eq(TmsZipZoneEntity::getRegionId, tmsZipZone.getRegionId()),false))) {
            return R.failed("tms.zip.zone.route.number.exists", tmsZipZone.getRouteNumber());
        }
        return tmsZipZoneService.addZipZone(tmsZipZone);
    }

    /**
     * 修改TMS区域邮编分区
     * @param tmsZipZone TMS区域邮编分区
     * @return R
     */
    @Operation(summary = "修改TMS区域邮编分区" , description = "修改TMS区域邮编分区" )
    @SysLog("修改TMS区域邮编分区" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_edit')" )
    public R updateById(@RequestBody TmsZipZoneEntity tmsZipZone) {
        return R.ok(tmsZipZoneService.updateZipZone(tmsZipZone));
    }

    /**
     * 通过id删除TMS区域邮编分区
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除TMS区域邮编分区" , description = "通过id删除TMS区域邮编分区" )
    @SysLog("通过id删除TMS区域邮编分区" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsZipZoneService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导入邮编分区
     * @param file
     * @return
     */
    @Operation(summary = "导入邮编分区" , description = "导入邮编分区" )
    @SysLog("导入邮编分区" )
    @PostMapping("/_import")
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_import')")
    public R importOrders(@RequestParam("file") MultipartFile file, @RequestParam("groupId") Long groupId) {
        return tmsZipZoneService.processFile(file,groupId);
    }

    /**
     * 导出excel 表格
     * @param tmsZipZone 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsZipZone_export')" )
    public List<TmsZipZoneEntity> export(TmsZipZoneEntity tmsZipZone,Long[] ids) {
        return tmsZipZoneService.list(Wrappers.lambdaQuery(tmsZipZone).in(ArrayUtil.isNotEmpty(ids), TmsZipZoneEntity::getId, ids));
    }

    @Operation(summary = "测试谷歌" , description = "测试谷歌" )
    @GetMapping("/listByRegionId" )
    public R listByRegionId() {
        // 记录轨迹
        //orderTrackService.saveTrack("KHDH20250319047424", EntrustedOrderStatus.INCOMPLETE.getValue(), "", "测试测试", 1);
        return R.ok();
    }


    @Operation(summary = "通过邮编查询覆盖区域" , description = "通过邮编查询覆盖区域" )
    @GetMapping("/getGeoByZip" )
    public R getGeoByZip(Long id) {
       return tmsZipZoneService.getGeoByZip(id);
    }


    @Operation(summary = "通过邮编查询覆盖区域(大区管理)" , description = "通过邮编查询覆盖区域（大区管理）" )
    @GetMapping("/getGeoByZip_bigArea" )
    public R getGeoByZip_bigArea(Long id) {
       return tmsZipZoneService.getGeoByZip_bigArea(id);
    }


}