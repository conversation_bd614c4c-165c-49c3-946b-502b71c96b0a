package com.jygjexp.jynx.tms.controller.api;


import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsThirdPartPostEntity;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsThirdPartPostService;
import com.jygjexp.jynx.tms.vo.ExchangeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: huqiuli
 * @Description: 换单接口API
 * @Date: 2025/6/06 22:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/thirdPartPost")
@Tag(description = "tmsThirdPartPost", name = "换单接口API")
public class ApiThirdPartPostController {

    private final TmsThirdPartPostService  tmsThirdPartPostService;
    private final TmsCustomerOrderService  tmsCustomerOrderService;


    @Operation(summary = "保存订单接口", description = "保存订单接口")
    @PostMapping("/save")
    @Inner(value = false)
    public R save(@RequestBody TmsThirdPartPostEntity tmsThirdPartPost) {
        return R.ok(tmsThirdPartPostService.save(tmsThirdPartPost));
    }



    @Operation(summary = "换单", description = "换单")
    @PostMapping("/exchangeOrder")
    @Inner(value = false)
    public R exchangeOrder(@RequestBody ExchangeVo vo) {
        return tmsCustomerOrderService.exchangeOrder(vo);
    }
}
