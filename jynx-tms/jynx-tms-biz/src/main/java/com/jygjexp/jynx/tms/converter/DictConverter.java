package com.jygjexp.jynx.tms.converter;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.admin.api.feign.RemoteDictService;
import com.jygjexp.jynx.common.core.util.RetOps;
import com.jygjexp.jynx.tms.annotation.DictKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.context.i18n.LocaleContextHolder;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2025/8/22 18:21
 * @Created guqingren
 */

public class DictConverter implements Converter<Integer> {

    private final RemoteDictService remoteDictService = SpringUtil.getBean(RemoteDictService.class);

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        //获取字典key
        String str = String.valueOf(value);
        Field field = contentProperty.getField();
        DictKey annotation = field.getAnnotation(DictKey.class);
        if (annotation == null) {
            return new WriteCellData<>(str);
        }

        String dictKey = annotation.value();
        if (StrUtil.isBlank(dictKey)) {
            return new WriteCellData<>(str);
        }

        if (ObjUtil.isNull(remoteDictService)) {
            return new WriteCellData<>(str);
        }

        //获取字典item
        List<Msg> msgs = RetOps.of(remoteDictService.getDictByType(dictKey))
                .getData()
                .orElse(Collections.emptyList())
                .stream()
                .map(Msg::of)
                .collect(Collectors.toList());

        //国际化
        Locale locale = LocaleContextHolder.getLocale();
        for (Msg msg : msgs) {
            if (StrUtil.equals(msg.itemValue, str)) {
                Map<String, Object> map = BeanUtil.beanToMap(msg);
                if (map.containsKey(locale.getLanguage())) {
                    return new WriteCellData<>(map.get(locale.getLanguage()).toString());
                }
            }
        }
        return new WriteCellData<>(str);
    }

    @Data
    @AllArgsConstructor
    public static class Msg {
        private String itemValue;
        private String zh;
        private String en;

        public static Msg of(SysDictItem dict) {
            String itemValue = dict.getItemValue();
            String zh = dict.getLabel();
            String en = dict.getDescription();
            return new Msg(itemValue, zh, en);
        }
    }
}
