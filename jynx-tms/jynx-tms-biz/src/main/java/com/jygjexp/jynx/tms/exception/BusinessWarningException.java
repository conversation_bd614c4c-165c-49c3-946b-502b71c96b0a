package com.jygjexp.jynx.tms.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 业务警告异常（不回滚事务）
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessWarningException extends RuntimeException {
    private int errorCode;

    private String errorMessage;

    public BusinessWarningException(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    public BusinessWarningException( String errorMessage) {
        this.errorCode = 500;
        this.errorMessage = errorMessage;
    }
}