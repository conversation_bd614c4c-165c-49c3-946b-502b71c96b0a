package com.jygjexp.jynx.tms.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 自定义异常类
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomBusinessException extends RuntimeException {
    /**
     * 成功标记
     */
    private final static Integer SUCCESS = 0;
    /**
     * 失败标记
     */
    private final static Integer FAIL = 1;

    private int errorCode;
    private String errorMessage;

    public CustomBusinessException(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    public CustomBusinessException(String errorMessage) {
        this.errorCode = FAIL;
        this.errorMessage = errorMessage;
    }

}
