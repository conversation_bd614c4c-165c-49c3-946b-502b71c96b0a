package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;

import java.util.List;

public interface TmsBaseCityStreetPostcodeService extends IService<TmsBaseCityStreetPostcodeEntity> {

    /**
     * 基于MySQL的地址自动补全搜索
     *
     * @param postcode 六位邮编（必填）
     * @param keyword 搜索关键词（可选）
     * @param limit 返回结果的最大条数
     * @return 匹配的地址列表
     */
    List<TmsBaseCityStreetPostcodeEntity> searchAddressAutocomplete(String postcode, String keyword, Integer limit);

    /**
     * 调用加拿大邮政API获取地址数据
     *
     * @param postcode 六位邮编
     * @param keyword 搜索关键词
     * @param limit 返回条数限制
     * @return 地址列表
     */
    List<TmsBaseCityStreetPostcodeEntity> getAddressFromCanadaPostApi(String postcode, String keyword, Integer limit);

    /**
     * 扫描最近24小时订单中的邮编并补充地址库
     */
    void syncRecentOrderPostcodes();

//    /**
//     * 地址自动补全搜索
//     *
//     * @param keyword 搜索关键词（支持邮编/城市/街道）
//     * @param limit 返回结果的最大条数
//     * @return 匹配的地址列表
//     */
//    List<TmsBaseCityStreetPostcodeEntity> searchAddressAutocomplete(String keyword, Integer limit);
//
//    /**
//     * 同步数据到Elasticsearch
//     * 将数据库中的地址数据同步到ES索引中
//     */
//    void syncDataToElasticsearch();
}