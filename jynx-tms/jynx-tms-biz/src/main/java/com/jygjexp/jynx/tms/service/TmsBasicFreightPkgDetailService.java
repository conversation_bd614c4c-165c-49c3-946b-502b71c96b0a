package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsBasicFreightPkgDetailEntity;
import com.jygjexp.jynx.tms.vo.TmsBasicFreightPkgDetailAddVo;

import java.util.List;

public interface TmsBasicFreightPkgDetailService extends IService<TmsBasicFreightPkgDetailEntity> {

    // 新增明细
    R addDetail(List<TmsBasicFreightPkgDetailAddVo> addVo);
}