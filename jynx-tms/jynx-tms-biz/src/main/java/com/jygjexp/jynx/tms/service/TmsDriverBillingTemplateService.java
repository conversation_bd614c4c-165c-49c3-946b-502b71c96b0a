package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsDriverBillingTemplateEntity;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplateDetailVo;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplatePageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverBillingTemplateRequestVo;

/**
 * 司机计费模板服务接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface TmsDriverBillingTemplateService extends IService<TmsDriverBillingTemplateEntity> {

    // 分页查询
    Page<TmsDriverBillingTemplatePageVo> search(Page page, TmsDriverBillingTemplatePageVo tmsDriverBillingTemplate);

    /**
     * 新增司机计费模板（包含配置信息）
     *
     * @param requestVo 请求参数
     * @return 操作结果
     */
    R saveTemplate(TmsDriverBillingTemplateRequestVo requestVo);

    /**
     * 修改司机计费模板（包含配置信息）
     *
     * @param requestVo 请求参数
     * @return 操作结果
     */
    R updateTemplate(TmsDriverBillingTemplateRequestVo requestVo);

    /**
     * 查看司机计费模板详情（包含配置信息）
     *
     * @param id 模板ID
     * @return 详情信息
     */
    R<TmsDriverBillingTemplateDetailVo> getTemplateDetail(Long id);

    /**
     * 删除司机计费模板（级联删除配置信息）
     *
     * @param id 模板ID
     * @return 操作结果
     */
    R deleteTemplate(Long id);

    /**
     * 批量删除司机计费模板（级联删除配置信息）
     *
     * @param ids 模板ID列表
     * @return 操作结果
     */
    R deleteTemplates(Long[] ids);
}