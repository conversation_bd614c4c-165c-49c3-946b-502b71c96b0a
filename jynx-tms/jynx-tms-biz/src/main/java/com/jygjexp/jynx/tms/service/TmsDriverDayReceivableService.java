package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsDriverDayReceivableEntity;
import com.jygjexp.jynx.tms.vo.TmsDriverDayReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPieceCalculationRequestVo;

import java.time.LocalDate;

public interface TmsDriverDayReceivableService extends IService<TmsDriverDayReceivableEntity> {

    // 分页查询
    Page<TmsDriverDayReceivablePageVo> search(Page page, TmsDriverDayReceivablePageVo vo);

    /**
     * 计算司机计件模式下的每日应收费用
     *
     * @param requestVo 计算请求参数
     * @return 计算结果
     */
    R calculateDriverPieceFee(TmsDriverPieceCalculationRequestVo requestVo);

}