package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsAppExceptionUploadDto;
import com.jygjexp.jynx.tms.entity.TmsExceptionManagementEntity;
import com.jygjexp.jynx.tms.vo.TmsExceptionManagementPageVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppMessageVo;
import org.springframework.web.bind.annotation.PathVariable;

public interface TmsExceptionManagementService extends IService<TmsExceptionManagementEntity> {

    // 分页查询
    Page<TmsExceptionManagementEntity> search(Page page, TmsExceptionManagementPageVo vo);

    // 异常上报
    R reporting(TmsExceptionManagementEntity tmsExceptionManagement);

    // 处理异常
    R handling(Long exceptionId,Integer handlingPlan, String handlingDescription);

    // 轮询消息
    R getMessages(Long userId);

    // 批量处理消息
    R disposeMessages (TmsAppMessageVo vo);

    //根据调度单号查询异常详情信息
    R getListByDispatchOrderNo(String dispatchOrderNo);


}