package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsInventoryManagementEntity;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsInventoryManagementListVo;
import com.jygjexp.jynx.tms.vo.TmsInventoryManagementPageVo;

import java.util.List;

public interface TmsInventoryManagementService extends IService<TmsInventoryManagementEntity> {

    // 分页查询库存列表
    Page<TmsInventoryManagementPageVo> search(Page page, TmsInventoryManagementPageVo entity);

    // 仓库跟踪单列表
    Page<TmsCustomerOrderPageVo> getWarehouseOrderPage(Page page, TmsCustomerOrderPageVo vo);

    // 跟踪单库存详情
    R getZDJOrderDetail(String entrustedOrderNumber, Long warehouseId);

    // 通过id查询库存详情
    TmsInventoryManagementEntity selectById(Long id);

}