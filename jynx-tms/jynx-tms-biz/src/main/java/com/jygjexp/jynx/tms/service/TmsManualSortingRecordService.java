package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity;
import com.jygjexp.jynx.tms.vo.TmsManualSortingRecordPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsManualSortingRecordExcelVo;

import java.util.List;

public interface TmsManualSortingRecordService extends IService<TmsManualSortingRecordEntity> {

    // 分拣管理分页
    Page<TmsManualSortingRecordPageVo> search(Page page, TmsManualSortingRecordPageVo vo);

    // 导出分拣管理列表
    List<TmsManualSortingRecordExcelVo> getManualSortingRecordExcel(TmsManualSortingRecordPageVo vo,Long[] ids);

}