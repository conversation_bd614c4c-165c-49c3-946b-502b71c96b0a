package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsLineHaulOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderLineHaulRelationEntity;

import java.util.List;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2025/4/13 21:16
 */
public interface TmsOrderLineHaulRelationService extends IService<TmsOrderLineHaulRelationEntity> {

    /**
     * 创建关联关系
     */
    R createRelation(Long customerOrderId, String lineHaulNo, Integer relationType);

    /**
     * 批量创建关联关系
     */
    R batchCreateRelation(List<Long> customerOrderIds, String lineHaulNo, Integer relationType);

    /**
     * 根据干线单号查询关联的客户订单
     */
    List<TmsCustomerOrderEntity> getCustomerOrdersByLineHaulNo(String lineHaulNo);

    /**
     * 根据客户订单ID查询关联的干线单号
     */
    List<String> getLineHaulNosByCustomerOrderId(Long customerOrderId);

    /**
     * 删除指定干线单号的所有关联
     */
    R deleteRelationsByLineHaulNo(String lineHaulNo);

    /**
     * 删除指定客户订单的所有关联
     */
    R deleteRelationsByCustomerOrderId(Long customerOrderId);

}
