package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionDetailEntity;
import com.jygjexp.jynx.tms.vo.TmsServiceQuotePricePageVo;
import com.jygjexp.jynx.tms.vo.TmsServiceReginDetailPageVo;
import com.jygjexp.jynx.tms.vo.TmsServiceReginPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceQuotePriceExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetaiUnreachabllExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetailExcelVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface TmsServiceRegionDetailService extends IService<TmsServiceRegionDetailEntity> {

    // 分页查询
    Page<TmsServiceReginDetailPageVo> search(Page page, TmsServiceReginDetailPageVo vo);

    // 导出可达邮编配置
    List<TmsServiceReginDetailExcelVo> reachableRegionExport(TmsServiceReginDetailPageVo tmsServiceRegionDetail, Long[] ids);

    // 导出可达邮编配置
    List<TmsServiceReginDetaiUnreachabllExcelVo> unreachableRegionExport(TmsServiceReginDetailPageVo tmsServiceRegionDetail, Long[] ids);

    // 导入可达分区邮编配置
    R reachableRegionImport(MultipartFile file,Long regionId);

    // 导入不可达分区邮编配置
    R unreachableRegionImport(MultipartFile file, Long regionId);

}