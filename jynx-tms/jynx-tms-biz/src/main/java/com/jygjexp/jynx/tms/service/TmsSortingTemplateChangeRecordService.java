package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.TemplateChangeRecordQueryDTO;
import com.jygjexp.jynx.tms.entity.TmsSortingRuleEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingTemplateChangeRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingTemplateEntity;
import com.jygjexp.jynx.tms.vo.SoringTemplateChangeRecordVO;

import java.util.List;

public interface TmsSortingTemplateChangeRecordService extends IService<TmsSortingTemplateChangeRecordEntity> {


    Page<SoringTemplateChangeRecordVO> selectPage(Page page, TemplateChangeRecordQueryDTO queryDTO);

    // 异步记录日志
    void createTemplateChangeRecordAsync(TmsSortingTemplateChangeRecordEntity saveEntity);


    // 构建分拣日志
    TmsSortingTemplateChangeRecordEntity structChangeRecordEntity(TmsSortingTemplateEntity newSortingTemplate,
                                                                  TmsSortingTemplateEntity oldSortingTemplate,
                                                                  List<TmsSortingRuleEntity> newTmsSortingRuleList,
                                                                  List<TmsSortingRuleEntity> oldTmsSortingRuleList,
                                                                  Integer operationType,
                                                                  String operationName);

}
