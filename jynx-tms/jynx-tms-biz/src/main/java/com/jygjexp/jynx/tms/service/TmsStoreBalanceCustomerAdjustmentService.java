package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceCustomerAdjustmentQueryDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceCustomerAdjustmentEntity;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceCustomerAdjustmentExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceCustomerAdjustmentPageVo;

import java.util.List;

public interface TmsStoreBalanceCustomerAdjustmentService extends IService<TmsStoreBalanceCustomerAdjustmentEntity> {

    Page<TmsStoreBalanceCustomerAdjustmentPageVo> pageAdjBalance(Page page, TmsStoreBalanceCustomerAdjustmentQueryDto tmsStoreBalanceCustomerAdjustment);

    boolean saveAdjBalance(TmsStoreBalanceCustomerAdjustmentEntity tmsStoreBalanceCustomerAdjustment);

    List<TmsStoreBalanceCustomerAdjustmentExcelVo> export(TmsStoreBalanceCustomerAdjustmentQueryDto tmsStoreBalanceCustomerAdjustment);
}