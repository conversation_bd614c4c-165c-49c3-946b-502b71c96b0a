package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderExceptionEntity;

import java.util.List;

public interface TmsStoreOrderExceptionService extends IService<TmsStoreOrderExceptionEntity> {

    List<TmsStoreOrderExceptionEntity> getStoreOrderExceptionsByStoreOrderId(Long id);

    void saveStoreMessageTrace(TmsStoreOrderExceptionEntity tmsStoreMessageTraceService);
}