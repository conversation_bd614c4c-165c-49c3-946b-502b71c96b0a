package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.StorePromotionCustomerOrderDTO;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionCustomerOrderEntity;

import java.util.List;

public interface TmsStorePromotionCustomerOrderService extends IService<TmsStorePromotionCustomerOrderEntity> {

    /**
     * 获取当前用户绑定的订单
     * @param promotionCode
     * @param storeCustomerId
     * @return
     */
    List<TmsStorePromotionCustomerOrderEntity> selectByCodeAndCustomerId(String promotionCode, Long storeCustomerId);

    /**
     * 保存优惠码绑定的客户和订单
     * @param promotionCustomerOrderDTO
     */
    void saveStorePromotionCustomerOrder(StorePromotionCustomerOrderDTO promotionCustomerOrderDTO);
}
