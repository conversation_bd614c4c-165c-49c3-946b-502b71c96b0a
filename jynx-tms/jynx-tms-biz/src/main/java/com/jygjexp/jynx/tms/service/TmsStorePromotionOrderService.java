package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.StorePromotionOrderDTO;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionOrderEntity;

public interface TmsStorePromotionOrderService extends IService<TmsStorePromotionOrderEntity> {

    /**
     * 根据优惠码id查询佣金结算
     * @param promotionCodeId
     * @return
     */
    TmsStorePromotionOrderEntity getByPromotionCodeId(Long promotionCodeId);


    /**
     * 保存优惠码订单信息
     * @param storePromotionOrderDTO
     */
    void saveStorePromotionOrder(StorePromotionOrderDTO storePromotionOrderDTO);
}
