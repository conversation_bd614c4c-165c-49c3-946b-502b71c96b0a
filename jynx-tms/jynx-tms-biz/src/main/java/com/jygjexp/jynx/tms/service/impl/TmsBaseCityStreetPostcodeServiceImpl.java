package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsBaseCityStreetPostcodeEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.mapper.TmsBaseCityStreetPostcodeMapper;
import com.jygjexp.jynx.tms.service.TmsBaseCityStreetPostcodeService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONArray;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 加拿大邮编地址库服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-02 16:17:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsBaseCityStreetPostcodeServiceImpl extends ServiceImpl<TmsBaseCityStreetPostcodeMapper, TmsBaseCityStreetPostcodeEntity> implements TmsBaseCityStreetPostcodeService {

    private final TmsBaseCityStreetPostcodeMapper tmsBaseCityStreetPostcodeMapper;
    private final TmsCustomerOrderService tmsCustomerOrderService;

    /**
     * 基于MySQL的地址自动补全搜索
     */
    @Override
    public List<TmsBaseCityStreetPostcodeEntity> searchAddressAutocomplete(String postcode, String keyword, Integer limit) {
        if (StrUtil.isBlank(postcode)) {
            return Collections.emptyList();
        }

        // 限制返回结果数量
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 30) {
            limit = 30;
        }

        // 首先从数据库查询
        List<TmsBaseCityStreetPostcodeEntity> dbResults = searchFromDatabase(postcode, keyword, limit);

        // 如果数据库中有数据，直接返回
        if (!dbResults.isEmpty()) {
            log.debug("从数据库查询到{}条记录，邮编: {}, 关键词: {}", dbResults.size(), postcode, keyword);
            return dbResults;
        }

        // 如果数据库中没有数据，调用加拿大邮政API
        log.info("数据库中未找到邮编{}的数据，调用加拿大邮政API", postcode);
        return getAddressFromCanadaPostApi(postcode, keyword, limit);
    }

    /**
     * 从数据库搜索地址
     */
    private List<TmsBaseCityStreetPostcodeEntity> searchFromDatabase(String postcode, String keyword, Integer limit) {
        LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = new LambdaQueryWrapper<>();

        // 只查询有效状态的数据
        wrapper.eq(TmsBaseCityStreetPostcodeEntity::getStatus, 1);
        // 六位邮编精确匹配
        wrapper.eq(TmsBaseCityStreetPostcodeEntity::getSixPostCode, postcode.toUpperCase());

        // 如果有关键词，进行模糊匹配
        if (StrUtil.isNotBlank(keyword)) {
            String searchKeyword = keyword.trim().toUpperCase();
            wrapper.and(w -> w
                .like(TmsBaseCityStreetPostcodeEntity::getProvince, searchKeyword)
                .or().like(TmsBaseCityStreetPostcodeEntity::getCity, searchKeyword)
                .or().like(TmsBaseCityStreetPostcodeEntity::getRstreet, searchKeyword)
            );
        }

        // 按相关性排序：省份、城市、街道
        wrapper.orderByAsc(TmsBaseCityStreetPostcodeEntity::getProvince)
               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getCity)
               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getRstreet);

        // 限制返回数量
        wrapper.last("LIMIT " + limit);

        return this.list(wrapper);
    }

    /**
     * 调用加拿大邮政API获取地址数据
     */
    @Override
    public List<TmsBaseCityStreetPostcodeEntity> getAddressFromCanadaPostApi(String postcode, String keyword, Integer limit) {
        List<TmsBaseCityStreetPostcodeEntity> result = new ArrayList<>();

        try {
            OkHttpClient client = new OkHttpClient();

            // 构建搜索文本：如果有关键词，组合邮编和关键词；否则只使用邮编
            String searchText = StrUtil.isNotBlank(keyword) ? postcode + " " + keyword : postcode;

            String url = "https://ws1.postescanada-canadapost.ca/Capture/Interactive/Find/v1.00/json3ex.ws" +
                    "?Key=EA98-JC42-TF94-JK98" +
                    "&Text=" + searchText +
                    "&Container=" +
                    "&Origin=CAN" +
                    "&Countries=CAN" +
                    "&Datasets=" +
                    "&Limit=" + limit +
                    "&Filter=" +
                    "&Language=en" +
                    "&$block=true" +
                    "&$cache=true" +
                    "&SOURCE=PCA-SCRIPT";

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("origin", "https://www.canadapost-postescanada.ca")
                    .addHeader("referer", "https://www.canadapost-postescanada.ca/cpc/en/tools/find-a-postal-code.page")
                    .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.body() != null) {
                    String json = response.body().string();
                    log.debug("加拿大邮政API响应: {}", json);

                    // 解析API响应并转换为实体对象
                    result = parseCanadaPostApiResponse(json, postcode);
                }
            }
        } catch (Exception e) {
            log.error("调用加拿大邮政API失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析加拿大邮政API响应
     */
    private List<TmsBaseCityStreetPostcodeEntity> parseCanadaPostApiResponse(String jsonResponse, String postcode) {
        List<TmsBaseCityStreetPostcodeEntity> result = new ArrayList<>();

        try {
            JSONObject jsonObject = JSONUtil.parseObj(jsonResponse);
            JSONArray items = jsonObject.getJSONArray("Items");

            if (items != null) {
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.getJSONObject(i);

                    TmsBaseCityStreetPostcodeEntity entity = new TmsBaseCityStreetPostcodeEntity();

                    // 解析地址信息
                    String text = item.getStr("Text", "");
                    String description = item.getStr("Description", "");

                    // 从API响应中提取地址组件
                    parseAddressComponents(entity, text, description, postcode);

                    // 设置基本信息
                    entity.setStatus(1);
                    entity.setMemo("API获取");
                    entity.setCreateDate(LocalDateTime.now());
                    entity.setVersion(1);

                    result.add(entity);
                }
            }
        } catch (Exception e) {
            log.error("解析加拿大邮政API响应失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析地址组件
     */
    private void parseAddressComponents(TmsBaseCityStreetPostcodeEntity entity, String text, String description, String postcode) {
        // 设置邮编
        entity.setSixPostCode(postcode.toUpperCase());
        if (postcode.length() >= 3) {
            entity.setThreePostCode(postcode.substring(0, 3).toUpperCase());
        }

        // 解析地址文本，通常格式为: "Street, City, Province PostalCode"
        if (StrUtil.isNotBlank(text)) {
            String[] parts = text.split(",");
            if (parts.length >= 3) {
                // 街道
                entity.setRstreet(parts[0].trim());
                // 城市
                entity.setCity(parts[1].trim());
                // 省份（可能包含邮编）
                String provinceAndPostal = parts[2].trim();
                String[] provinceParts = provinceAndPostal.split("\\s+");
                if (provinceParts.length >= 1) {
                    entity.setProvince(provinceParts[0].trim());
                }
            } else if (parts.length == 2) {
                // 城市和省份
                entity.setCity(parts[0].trim());
                String provinceAndPostal = parts[1].trim();
                String[] provinceParts = provinceAndPostal.split("\\s+");
                if (provinceParts.length >= 1) {
                    entity.setProvince(provinceParts[0].trim());
                }
            }
        }

        // 如果某些字段为空，使用description作为补充
        if (StrUtil.isBlank(entity.getCity()) && StrUtil.isNotBlank(description)) {
            entity.setCity(description);
        }
    }

    /**
     * 扫描最近24小时订单中的邮编并补充地址库
     */
    @Override
    public void syncRecentOrderPostcodes() {
        log.info("开始扫描最近24小时订单中的邮编...");

        try {
            // 查询最近24小时的订单
            LocalDateTime startTime = LocalDateTime.now().minusHours(24);
            LambdaQueryWrapper<TmsCustomerOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.ge(TmsCustomerOrderEntity::getCreateTime, startTime)
                       .isNotNull(TmsCustomerOrderEntity::getDestPostalCode)
                       .ne(TmsCustomerOrderEntity::getDestPostalCode, "");

            List<TmsCustomerOrderEntity> recentOrders = tmsCustomerOrderService.list(orderWrapper);
            log.info("查询到最近24小时内{}条订单", recentOrders.size());

            int syncCount = 0;
            for (TmsCustomerOrderEntity order : recentOrders) {
                String postcode = order.getDestPostalCode();
                if (StrUtil.isBlank(postcode) || postcode.length() != 6) {
                    continue;
                }

                // 检查邮编是否已存在于地址库中
                LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> existWrapper = new LambdaQueryWrapper<>();
                existWrapper.eq(TmsBaseCityStreetPostcodeEntity::getSixPostCode, postcode.toUpperCase());
                long existCount = this.count(existWrapper);

                if (existCount == 0) {
                    // 邮编不存在，根据订单信息创建地址记录
                    TmsBaseCityStreetPostcodeEntity entity = createEntityFromOrder(order);
                    if (entity != null) {
                        this.save(entity);
                        syncCount++;
                        log.debug("新增地址记录: 邮编={}, 城市={}", entity.getSixPostCode(), entity.getCity());
                    }
                }
            }

            log.info("定时任务完成，共同步{}条新地址记录", syncCount);

        } catch (Exception e) {
            log.error("扫描订单邮编定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据订单信息创建地址实体
     */
    private TmsBaseCityStreetPostcodeEntity createEntityFromOrder(TmsCustomerOrderEntity order) {
        try {
            TmsBaseCityStreetPostcodeEntity entity = new TmsBaseCityStreetPostcodeEntity();

            String postcode = order.getDestPostalCode().toUpperCase();
            entity.setSixPostCode(postcode);
            if (postcode.length() >= 3) {
                entity.setThreePostCode(postcode.substring(0, 3));
            }

            // 从订单地址中解析城市、省份、街道信息
            String destAddress = order.getDestAddress();
            if (StrUtil.isNotBlank(destAddress)) {
                parseOrderAddress(entity, destAddress);
            }

            // 设置基本信息
            entity.setStatus(1);
            entity.setMemo("订单同步");
            entity.setCreateDate(LocalDateTime.now());
            entity.setVersion(1);

            return entity;

        } catch (Exception e) {
            log.error("根据订单创建地址实体失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析订单地址信息
     */
    private void parseOrderAddress(TmsBaseCityStreetPostcodeEntity entity, String address) {
        // 简单的地址解析逻辑，可根据实际地址格式调整
        String[] parts = address.split(",");
        if (parts.length >= 3) {
            entity.setRstreet(parts[0].trim());
            entity.setCity(parts[1].trim());
            entity.setProvince(parts[2].trim());
        } else if (parts.length == 2) {
            entity.setCity(parts[0].trim());
            entity.setProvince(parts[1].trim());
        } else {
            entity.setCity(address.trim());
        }
    }

//    private final AddressSearchService addressSearchService;
//
//    /**
//     * 是否启用Elasticsearch搜索
//     */
//    //@Value("${address.search.elasticsearch.enabled:false}")
//    private final boolean elasticsearchEnabled=Boolean.TRUE;
//
//    /**
//     * 地址自动补全搜索
//     *
//     * @param keyword 搜索关键词（支持邮编/城市/街道）
//     * @param limit 返回结果的最大条数
//     * @return 匹配的地址列表
//     */
//    public List<TmsBaseCityStreetPostcodeEntity> searchAddressAutocomplete(String keyword, Integer limit) {
//        if (StrUtil.isBlank(keyword)) {
//            return Collections.emptyList();
//        }
//
//        // 限制返回结果数量，避免过多数据
//        if (limit == null || limit <= 0) {
//            limit = 10;
//        }
//        if (limit > 50) {
//            limit = 50; // 最大限制50条
//        }
//
//        // 如果启用了Elasticsearch，优先使用ES搜索
//        if (elasticsearchEnabled) {
//            try {
//                List<AddressDocument> esResults = Collections.emptyList();
//
//                // 优先使用ElasticsearchRestTemplate查询（更可靠，避免通配符问题）
//                esResults = addressSearchService.searchAddressWithElasticsearch(keyword, limit);
//                if (!esResults.isEmpty()) {
//                    log.debug("ElasticsearchRestTemplate搜索成功，关键词: {}, 结果数量: {}", keyword, esResults.size());
//                    return convertDocumentsToEntities(esResults);
//                }
//
//                // 如果ElasticsearchRestTemplate无结果，尝试Spring Data方法作为备选
//                esResults = addressSearchService.searchAddressWithSpringData(keyword, limit);
//                if (!esResults.isEmpty()) {
//                    log.debug("Spring Data备选搜索成功，关键词: {}, 结果数量: {}", keyword, esResults.size());
//                    return convertDocumentsToEntities(esResults);
//                }
//
//                log.warn("Elasticsearch所有搜索方式均无结果，降级到数据库搜索，关键词: {}", keyword);
//            } catch (Exception e) {
//                log.error("Elasticsearch搜索失败，降级到数据库搜索，关键词: {}, 错误: {}", keyword, e.getMessage(), e);
//            }
//        }
//
//        // 使用数据库搜索（降级方案）
//        return searchFromDatabase(keyword, limit);
//    }
//
//    /**
//     * 从数据库搜索（降级方案）
//     */
//    private List<TmsBaseCityStreetPostcodeEntity> searchFromDatabase(String keyword, Integer limit) {
//        // 构建查询条件
//        LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = new LambdaQueryWrapper<>();
//
//        // 只查询有效状态的数据
//        wrapper.eq(TmsBaseCityStreetPostcodeEntity::getMemo, "有效邮编");
//
//        // 关键词搜索：支持邮编、城市、街道的模糊匹配
//        String searchKeyword = keyword.trim().toUpperCase();
//
///*        wrapper.and(w -> w
//            // 三字邮编前缀匹配（优先级最高）
//            .likeRight(TmsBaseCityStreetPostcodeEntity::getThreePostCode, searchKeyword)
//            // 六字邮编前缀匹配
//            .or().likeRight(TmsBaseCityStreetPostcodeEntity::getSixPostCode, searchKeyword)
//            // 城市名称模糊匹配
//            .or().like(TmsBaseCityStreetPostcodeEntity::getCity, searchKeyword)
//            // 街道名称模糊匹配
//            .or().like(TmsBaseCityStreetPostcodeEntity::getRstreet, searchKeyword)
//        );*/
//
//        if (searchKeyword.matches("^[A-Z]\\d[A-Z](\\s?\\d[A-Z]\\d)?$")) {
//            // 输入像邮编，优先邮编匹配
//            wrapper.and(w -> w.likeRight(TmsBaseCityStreetPostcodeEntity::getThreePostCode, searchKeyword)
//                    .or().likeRight(TmsBaseCityStreetPostcodeEntity::getSixPostCode, searchKeyword));
//        } else {
//            // 输入像地名，优先匹配城市/街道
//            wrapper.and(w -> w.like(TmsBaseCityStreetPostcodeEntity::getCity, searchKeyword)
//                    .or().like(TmsBaseCityStreetPostcodeEntity::getRstreet, searchKeyword));
//        }
//
//        // 排序：优先显示邮编匹配的结果，然后按城市、街道排序
//        wrapper.orderByAsc(TmsBaseCityStreetPostcodeEntity::getThreePostCode)
//               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getCity)
//               .orderByAsc(TmsBaseCityStreetPostcodeEntity::getRstreet);
//
//        // 限制返回数量
//        wrapper.last("LIMIT " + limit);
//
//        return this.list(wrapper);
//    }
//
//    /**
//     * 将Elasticsearch文档转换为实体对象
//     */
//    private List<TmsBaseCityStreetPostcodeEntity> convertDocumentsToEntities(List<AddressDocument> documents) {
//        return documents.stream().map(doc -> {
//            TmsBaseCityStreetPostcodeEntity entity = new TmsBaseCityStreetPostcodeEntity();
//            entity.setId(Integer.valueOf(doc.getId()));
//            entity.setThreePostCode(doc.getThreePostCode());
//            entity.setSixPostCode(doc.getSixPostCode());
//            entity.setCity(doc.getCity());
//            entity.setProvince(doc.getProvince());
//            entity.setRstreet(doc.getStreet());
//            entity.setStatus(doc.getStatus());
//            // 重要：使用ES文档中的memo字段，确保字段映射一致性
//            entity.setMemo(doc.getMemo());
//            return entity;
//        }).collect(Collectors.toList());
//    }
//
//    /**
//     * 验证ES数据和数据库数据的一致性
//     * 用于调试和排查数据问题
//     */
//    public void validateDataConsistency(String keyword) {
//        log.info("开始验证数据一致性，关键词: {}", keyword);
//
//        try {
//            // 从数据库查询
//            List<TmsBaseCityStreetPostcodeEntity> dbResults = searchFromDatabase(keyword, 10);
//            log.info("数据库查询结果数量: {}", dbResults.size());
//            log.info("数据库查询条件: memo = '有效邮编'");
//
//            if (!dbResults.isEmpty()) {
//                TmsBaseCityStreetPostcodeEntity firstDb = dbResults.get(0);
//                log.info("数据库第一条记录: ID={}, 三字邮编={}, 城市={}, 街道={}, memo={}",
//                    firstDb.getId(), firstDb.getThreePostCode(), firstDb.getCity(), firstDb.getRstreet(), firstDb.getMemo());
//            }
//
//            // 从ES查询
//            List<AddressDocument> esResults = addressSearchService.searchAddressWithElasticsearch(keyword, 10);
//            log.info("ES查询结果数量: {}", esResults.size());
//            log.info("ES查询条件: memo = '有效邮编'");
//
//            if (!esResults.isEmpty()) {
//                AddressDocument firstEs = esResults.get(0);
//                log.info("ES第一条记录: ID={}, 三字邮编={}, 城市={}, 街道={}, memo={}",
//                    firstEs.getId(), firstEs.getThreePostCode(), firstEs.getCity(), firstEs.getStreet(), firstEs.getMemo());
//            }
//
//            // 验证查询条件一致性
//            log.info("查询条件一致性验证:");
//            log.info("- 数据库使用: wrapper.eq(TmsBaseCityStreetPostcodeEntity::getMemo, \"有效邮编\")");
//            log.info("- ES使用: {\"term\":{\"memo\":\"有效邮编\"}}");
//            log.info("- Spring Data使用: findByMemoAndXXX(\"有效邮编\", ...)");
//
//        } catch (Exception e) {
//            log.error("数据一致性验证失败: {}", e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 同步数据到Elasticsearch
//     * 将数据库中的地址数据同步到ES索引中
//     */
//    public void syncDataToElasticsearch() {
//        if (!elasticsearchEnabled) {
//            log.warn("Elasticsearch未启用，跳过数据同步");
//            return;
//        }
//
//        try {
//            log.info("开始同步地址数据到Elasticsearch...");
//
//            // 先清空现有索引
//            addressSearchService.deleteAllDocuments();
//
//            int pageSize = 2000; // 每次查询数量
//            int batchSize = 1000; // 每次写入 ES 的大小
//            int pageNum = 1;
//            int totalCount = 0;
//
//            while (true) {
//                Page<TmsBaseCityStreetPostcodeEntity> page = new Page<>(pageNum, pageSize);
//                LambdaQueryWrapper<TmsBaseCityStreetPostcodeEntity> wrapper = new LambdaQueryWrapper<>();
//                wrapper.eq(TmsBaseCityStreetPostcodeEntity::getMemo, "有效邮编");
//
//                // 分页查询
//                Page<TmsBaseCityStreetPostcodeEntity> entityPage = this.page(page, wrapper);
//                List<TmsBaseCityStreetPostcodeEntity> entities = entityPage.getRecords();
//
//                if (entities == null || entities.isEmpty()) {
//                    break; // 没有数据了，退出循环
//                }
//
//                // 转换为 ES 文档
//                List<AddressDocument> documents = entities.stream()
//                        .map(addressSearchService::convertToDocument)
//                        .collect(Collectors.toList());
//
//                // 分批写入 ES
//                for (int i = 0; i < documents.size(); i += batchSize) {
//                    int endIndex = Math.min(i + batchSize, documents.size());
//                    List<AddressDocument> batch = documents.subList(i, endIndex);
//                    addressSearchService.saveDocuments(batch);
//                    log.info("已同步第{}页的第{}批数据，共{}条", pageNum, (i / batchSize) + 1, batch.size());
//                }
//
//                totalCount += documents.size();
//                pageNum++;
//            }
//
//            log.info("地址数据同步完成，共同步{}条记录", totalCount);
//
//        } catch (Exception e) {
//            log.error("同步数据到Elasticsearch失败: {}", e.getMessage(), e);
//            throw new RuntimeException("数据同步失败", e);
//        }
//    }

}
