package com.jygjexp.jynx.tms.task;

import com.jygjexp.jynx.tms.service.TmsBaseCityStreetPostcodeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 地址库同步定时任务
 *
 * <AUTHOR>
 * @date 2025-09-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AddressSyncTask {

    private final TmsBaseCityStreetPostcodeService tmsBaseCityStreetPostcodeService;

    /**
     * 扫描最近24小时订单中的邮编并补充地址库
     * 建议配置为每天凌晨2点执行：0 0 2 * * ?
     */
    @XxlJob("syncRecentOrderPostcodes")
    public void syncRecentOrderPostcodes() {
/*        XxlJobLogger.log("定时任务：【地址库同步】于:{}，开始执行", LocalDateTime.now());
        log.info("开始执行地址库同步定时任务...");

        try {
            tmsBaseCityStreetPostcodeService.syncRecentOrderPostcodes();
            log.info("地址库同步定时任务执行完成");
            XxlJobLogger.log("定时任务：【地址库同步】执行完成，时间: {}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("地址库同步定时任务执行失败: {}", e.getMessage(), e);
            XxlJobLogger.log("定时任务：【地址库同步】执行失败: {}", e.getMessage());
            throw e; // 重新抛出异常，让XXL-Job知道任务失败
        }*/
    }
}
