ALTER TABLE nb_order ADD INDEX idx_order_id_desc (order_id DESC);
ALTER TABLE nb_order ADD INDEX idx_order_status (order_status);

-- EXPLAIN select t.order_id, t.merchant_id, t.p_driver_id, t.driver_id, t.order_no , t.sc_id, t.tc_id, t.region_id, t.region_code, t.contact_time , t.order_category, t.sms_time, t.order_type, t.express_category, t.failure_reason , t.transport_way, t.required_sign, t.last_route_time, t.add_time, t.batch_no , t.sub_batch_no, t.customer_order_no, t.customer_p_order_no, t.goods_desc, t.dest_name , t.dest_tel, t.dest_email, t.dest_country, t.dest_province, t.dest_city , t.dest_address1, t.dest_address2, t.dest_address3, t.dest_unit_no, t.dest_postal_code , t.dest_lat, t.dest_lng, t.dest_address_type, t.driver_remark, t.order_remark , t.pkg_weight, t.pkg_length, t.pkg_width, t.pkg_height, t.pkg_value , t.pkg_no, t.pkg_type, t.is_intercept, t.intercept_user_id, t.intercept_batch_id , t.intercept_remark, t.box_no, t.finished_time, t.delivery_status, t.order_status , t.pack_id, t.door_access_pwd, t.shipper_name, t.shipper_tel, t.shipper_country , t.shipper_province, t.shipper_city, t.shipper_address1, t.shipper_address2, t.shipper_address3 , t.shipper_postal_code, t.start_delivery_time, t.delivery_try, t.delivery_type, t.latest_delivery_time , t.deliveryed_time, t.pick_no, t.driver_pickup_time, t.sys_status, t.geo_times , t.tc_err, t.is_routed, t.express_type, t.danger_type, t.transport_type , t.failed_delivery_action, t.bag_number, t.is_mps, t.p_order_id, t.has_sub_order , t.r4m_order_id, t.sub_channel, t.eta, t.etd, t.ata , t.atd, t.jy_order_no, t.jy_order_id, t.jy_mark_no, t.fun_type , t.uni_delivered, t.uni_delivered_time, t.third_label, t.is_printed, t.in_nb_range , t.tag, t1.merchant_id, t1.name as merchantName, t2.driver_id, t2.driver_name , t2.first_name, t2.last_name, t2.email, t2.mobile, t3.id , t3.title as orderStatusTitle from nb_order t left join nb_merchant t1 on t1.merchant_id = t.merchant_id left join nb_driver t2 on t2.driver_id = t.driver_id left join nb_order_status t3 on t3.id = t.order_status order by t.order_id desc limit 10

-- SHOW INDEX FROM nb_order;

-- SET GLOBAL slow_query_log = 1;

-- 订单路径（签收记录）新增索引
ALTER TABLE nb_order_path ADD INDEX idx_order_status_timestamp (order_status, add_timestamp DESC);  -- 订单状态+时间复合索引
ALTER TABLE nb_order_path ADD INDEX idx_driver_id (driver_id);

-- 待打印订单 添加复合索引
ALTER TABLE nb_order ADD INDEX idx_printed_nb_range_order (is_printed, in_nb_range, order_id DESC);

-- 滞留订单 添加复合索引
ALTER TABLE nb_order ADD INDEX idx_order_status_add_time (order_status, add_time);
