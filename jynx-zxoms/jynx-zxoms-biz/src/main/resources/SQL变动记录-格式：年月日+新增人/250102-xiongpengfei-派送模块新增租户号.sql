ALTER TABLE `nb_app_version` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order_sign_image` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order_status` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order_status_modify_rel` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';

ALTER TABLE `nb_driver` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_merchant` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order_batch` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_order_path` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';

ALTER TABLE `nb_shelf` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_shelf_pkg_log` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_sms_log` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_sms_template` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_sorting_center` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_transfer_batch` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_transfer_batch_order` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_transfer_center` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';

ALTER TABLE `nb_price_district` ADD COLUMN `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户号';
ALTER TABLE `nb_price_district` ADD COLUMN `revision` BigINT NOT NULL  DEFAULT 1 COMMENT '乐观锁';
ALTER TABLE `nb_price_district` ADD COLUMN `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志';