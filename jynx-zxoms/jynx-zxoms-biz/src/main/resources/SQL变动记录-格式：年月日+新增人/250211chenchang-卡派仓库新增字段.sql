ALTER TABLE tms_warehouse ADD country_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国家';
ALTER TABLE tms_warehouse ADD province_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省';
ALTER TABLE tms_warehouse ADD city_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市';
ALTER TABLE tms_warehouse ADD timezone varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所在时区';
ALTER TABLE tms_warehouse ADD business_hours varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '营业时间';
ALTER TABLE tms_warehouse ADD lat DOUBLE DEFAULT 0 COMMENT '纬度';
ALTER TABLE tms_warehouse ADD lng DOUBLE DEFAULT 0 COMMENT '经度';
ALTER TABLE tms_warehouse ADD postal_code varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮编';
ALTER TABLE tms_warehouse ADD country_id INT NOT NULL COMMENT '国家id';
ALTER TABLE tms_warehouse ADD province_id INT NOT NULL COMMENT '省id';
ALTER TABLE tms_warehouse ADD city_id INT NOT NULL COMMENT '市/地区id';
